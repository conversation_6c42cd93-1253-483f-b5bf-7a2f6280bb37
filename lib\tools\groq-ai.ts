/**
 * Groq integration for the LLM tool
 */

// Import the Groq SDK
import Groq from "groq-sdk";

// Define interfaces for Groq processing
export interface GroqProcessingOptions {
  prompt: string;
  model?: string;
  modelOptions?: Record<string, any>;
}

/**
 * Process content with Groq
 * @param options - Processing options
 * @param options.prompt - The prompt to send to the LLM
 * @param options.model - The model to use (default: "deepseek-r1-distill-llama-70b")
 * @param options.modelOptions - Additional model-specific options
 * @returns The generated content
 */
export async function processWithGroq(options: GroqProcessingOptions): Promise<string> {
  try {
    const {
      prompt,
      model: modelToUse = "deepseek-r1-distill-llama-70b",
      modelOptions = {}
    } = options;

    // Create a mutable copy of the model name
    //let modelToUse = 'deepseek-r1-distill-llama-70b';

    //console.log(`Processing with Groq model: ${modelToUse}`);

    // Check if this is an OpenAI model mistakenly sent to Groq
    // const openaiModels = ['gpt-4o', 'gpt-4.1-2025-04-14', 'o3-2025-04-16', 'o3-mini-2025-01-31', 'o1-mini-2024-09-12'];
    // if (openaiModels.includes(modelToUse)) {
    //   console.error(`Error: Attempted to process OpenAI model "${modelToUse}" with Groq. This is not supported.`);
    //   console.warn(`Falling back to default Groq model "deepseek-r1-distill-llama-70b" instead.`);
    //   modelToUse = "deepseek-r1-distill-llama-70b";
    // }

    // Get the API key from environment variables
    const groqApiKey = process.env.GROQ_API_KEY || '********************************************************';

    if (!groqApiKey) {
      console.warn('GROQ_API_KEY environment variable is not set. Using development fallback mode.');

      // In development, return a mock response based on the model and prompt
      if (process.env.NODE_ENV === 'development') {
        return generateDevelopmentFallbackResponse(modelToUse, prompt);
      } else {
        throw new Error('GROQ_API_KEY environment variable is not set');
      }
    }

    // Initialize the Groq client with browser support
    const groq = new Groq({
      apiKey: groqApiKey,
      dangerouslyAllowBrowser: true // Enable browser environment support
    });

    // Check if it's a DeepSeek model to add specific parameters
    const isDeepSeek = modelToUse.toLowerCase().includes('deepseek');

    // Log the model being used
    //console.log(`Using Groq model: ${modelToUse} (DeepSeek: ${isDeepSeek})`);

    // Validate model
    const availableModels = getGroqModels();
    if (!availableModels.includes(modelToUse)) {
      console.warn(`Model ${modelToUse} not in available Groq models list. Available models: ${availableModels.join(', ')}`);
      // Continue anyway as Groq might have added new models
    }

    // Create the parameters object
    const params: any = {
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      model: modelToUse,
      temperature: modelOptions.temperature !== undefined ? modelOptions.temperature : 0.4,
      max_tokens: modelOptions.max_tokens !== undefined ? modelOptions.max_tokens :
                 (modelOptions.maxTokens !== undefined ? modelOptions.maxTokens : 7000),
      top_p: isDeepSeek ? 0.95 : undefined,
    };

    // Add any additional model options, but ensure we don't add maxTokens or reasoning
    // (which would cause an error since Groq expects max_tokens and doesn't support reasoning)
    const { maxTokens, reasoning, ...otherModelOptions } = modelOptions;
    Object.assign(params, otherModelOptions);

    // Add reasoning_format only for DeepSeek models
    if (isDeepSeek) {
      params.reasoning_format = "hidden";
    } else if (params.reasoning_format) {
      delete params.reasoning_format; // Remove if present from modelOptions for non-DeepSeek models
    }

    // Get chat completion using the Groq API
    const chatCompletion = await groq.chat.completions.create(params);

    // Return the text content from the completion
    return chatCompletion.choices[0]?.message?.content || "";
  } catch (error: any) {
    console.error("Error processing content with Groq:", error);
    return `Error from Groq: ${error.message || "Unknown error"}`;
  }
}

/**
 * Get available Groq models
 * @returns List of available models
 */
export function getGroqModels(): string[] {
  return [
    "llama-3.3-70b-versatile",
    "meta-llama/llama-4-maverick-17b-128e-instruct",
    "deepseek-r1-distill-llama-70b"
    // Note: OpenAI models like "gpt-4o" should not be used with Groq
  ];
}

/**
 * Generate a fallback response for development mode when no API key is available
 * @param model - The model that was requested
 * @param prompt - The prompt that was sent
 * @returns A mock response based on the model and prompt
 */
function generateDevelopmentFallbackResponse(model: string, prompt: string): string {
  //console.log(`Generating development fallback response for model: ${model}`);

  // If this is a prompt interpreter request (DeepSeek model)
  if (model.includes('deepseek') && prompt.includes('analyze the following prompt')) {
    return `
1. Specify the target audience (e.g., beginners, experts, general public)
2. Define the desired tone (formal, conversational, technical)
3. Indicate the preferred length or level of detail
4. Clarify the specific goal or outcome
5. Add relevant context or background information
6. Specify any format requirements
7. Include examples if helpful
8. Define any constraints or limitations
`;
  }

  // If this is a prompt optimizer request (Llama model)
  if (model.includes('llama') && prompt.includes('optimize the following prompt')) {
    const originalPromptMatch = prompt.match(/ORIGINAL PROMPT:\s*\n"""([\s\S]*?)"""/i);
    const originalPrompt = originalPromptMatch ? originalPromptMatch[1].trim() : "Unknown prompt";

    // Create an optimized prompt, not an answer to the prompt
    return `Provide a comprehensive analysis of ${originalPrompt.replace(/[?.,!]$/, '')} with the following characteristics:
1. Include historical context and development
2. Explain key concepts and terminology for a general audience
3. Present different perspectives or interpretations
4. Cite specific examples and reliable sources
5. Organize the information in a clear, logical structure
6. Use an informative, educational tone
7. Address common misconceptions
8. Conclude with the current state of understanding on this topic`;
  }

  // Generic fallback for other cases
  return `This is a development fallback response for the prompt: "${prompt.substring(0, 100)}..."

Since the GROQ_API_KEY is not set, this is a simulated response to allow development and testing to continue without the actual API.

In a production environment, please ensure the GROQ_API_KEY is properly set in your environment variables.`;
}

