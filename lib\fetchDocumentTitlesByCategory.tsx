import { collection, query, where, getDocs } from '@firebase/firestore';
import { db } from '../components/firebase';

export async function fetchDocumentTitlesByCategory(category: string, userId: string) {
    if (!category) {
        throw new Error('Category is required to fetch document titles.');
    }

    try {
        const q = query(
            collection(db, 'users', userId, 'files'),
            where('category', '==', category)
        );

        const querySnapshot = await getDocs(q);
        const documentTitlesArray: string[] = [];

        querySnapshot.forEach((doc) => {
            const data = doc.data();
            if (data && data.document_title) {
                documentTitlesArray.push(data.document_title); 
            }
        });

        //console.log("documentTitlesArray",documentTitlesArray)
        return documentTitlesArray.join(', '); 
    } catch (error) {
        console.error("Error fetching document titles by category:", error);
        throw new Error('Error fetching document titles by category');
    }
}
