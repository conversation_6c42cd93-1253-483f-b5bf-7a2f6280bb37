# Audio Troubleshooting Guide - ElevenLabs Voice Not Audible

## Issue Summary
The voice selection is now working correctly (agent configuration is being updated), but you cannot hear the AI voice responses during conversations.

## Potential Causes & Solutions

### 1. **Browser Audio Settings**

#### Check Browser Volume
- Ensure your browser tab is not muted
- Check browser volume mixer (Windows: Volume Mixer → Chrome/Edge)
- Verify system volume is not muted

#### Browser Permissions
- Check if the site has audio permissions
- Go to browser settings → Site permissions → Sound
- Ensure the site is allowed to play audio

### 2. **Audio Output Device**

#### Check Default Audio Device
- Verify correct audio output device is selected in Windows
- Test with other audio applications (YouTube, Spotify, etc.)
- Try switching audio output devices

#### Headphones/Speakers
- Test with different audio output devices
- Check if headphones/speakers are properly connected
- Verify audio cables and connections

### 3. **ElevenLabs Configuration Issues**

#### Agent Audio Settings
The agent configuration shows these audio settings:
```json
"tts": {
  "model_id": "eleven_flash_v2",
  "voice_id": "rCuVrCHOUMY3OwyJBJym",
  "agent_output_audio_format": "pcm_16000",
  "optimize_streaming_latency": 3
}
```

#### Potential Issues:
- **Audio Format**: `pcm_16000` might not be compatible with browser
- **Streaming Latency**: Setting of 3 might cause audio delays
- **Voice Model**: `eleven_flash_v2` might have audio output issues

### 4. **Browser Compatibility**

#### Supported Browsers
- Chrome (recommended)
- Edge
- Firefox (may have WebRTC issues)
- Safari (limited support)

#### WebRTC/Audio Context
- Check if browser supports Web Audio API
- Verify WebRTC is enabled
- Test in incognito mode to rule out extensions

### 5. **Network/Streaming Issues**

#### Connection Quality
- Check internet connection stability
- Test with different network (mobile hotspot)
- Verify firewall isn't blocking audio streams

#### ElevenLabs Service Status
- Check ElevenLabs status page
- Verify API limits haven't been exceeded
- Test with different voice models

## Debugging Steps

### Step 1: Browser Console Debugging
Open browser console and run:
```javascript
// Check audio capabilities
debugAudioSettings()

// Test voice selection
debugVoiceSelection('rCuVrCHOUMY3OwyJBJym')
```

### Step 2: Check Audio Logs
Look for these log patterns:
```
[AUDIO] Audio playback started
[AUDIO] Audio playback stopped
[AUDIO] Mode changed to: [mode]
[AUDIO] Status changed to: [status]
```

### Step 3: Test Different Voices
Try selecting different voices to see if the issue is voice-specific:
- Mia: `rCuVrCHOUMY3OwyJBJym`
- Morgan: `QQutlXbwqnU9C4Zprxnn`
- Dakota: `P7x743VjyZEOihNNygQ9`

### Step 4: Check Conversation Status
Verify the conversation is properly connected:
```javascript
// In browser console
//console.log("Conversation status:", conversation.status)
//console.log("Is speaking:", conversation.isSpeaking)
```

### Step 5: Test Audio Context
```javascript
// Test browser audio support
const AudioContext = window.AudioContext || window.webkitAudioContext
if (AudioContext) {
  const ctx = new AudioContext()
  //console.log("Audio context state:", ctx.state)
  //console.log("Sample rate:", ctx.sampleRate)
} else {
  console.error("AudioContext not supported")
}
```

## Quick Fixes to Try

### 1. **Refresh and Retry**
1. Refresh the page
2. Select a voice again
3. Start a new conversation
4. Speak to trigger AI response

### 2. **Browser Settings Reset**
1. Clear browser cache for the site
2. Reset site permissions
3. Disable browser extensions temporarily
4. Try incognito/private mode

### 3. **Audio Device Switch**
1. Switch to different audio output device
2. Test with headphones vs speakers
3. Adjust system volume levels

### 4. **Network Troubleshooting**
1. Test on different network
2. Disable VPN if active
3. Check firewall settings
4. Try mobile hotspot

## Advanced Debugging

### Check ElevenLabs WebSocket Connection
```javascript
// Monitor WebSocket messages in Network tab
// Look for audio data streams
// Check for connection errors
```

### Audio Format Testing
The current agent uses `pcm_16000` format. Consider testing with:
- Different audio formats
- Different sample rates
- Different voice models

### Browser Audio Debugging
```javascript
// Check audio permissions
navigator.permissions.query({name: 'microphone'}).then(result => {
  //console.log("Microphone permission:", result.state)
})

// Check audio devices
navigator.mediaDevices.enumerateDevices().then(devices => {
  //console.log("Audio devices:", devices.filter(d => d.kind.includes('audio')))
})
```

## Expected Behavior When Working

### Console Logs Should Show:
```
[AUDIO] Audio playback started
[CONVERSATION_START] ElevenLabs conversation started successfully
[AUDIO] AI response received: [response text]
[AUDIO] Mode changed to: speaking
```

### User Experience:
1. Select voice → Voice updates successfully
2. Start conversation → Connection established
3. Speak → AI responds with audible voice
4. Audio plays through selected output device

## If Nothing Works

### Last Resort Options:
1. **Test API Endpoint Directly**: Use the test endpoint to verify agent configuration
2. **Contact ElevenLabs Support**: Check if there are known issues with the agent
3. **Try Different Browser**: Test in Chrome, Edge, Firefox
4. **Check ElevenLabs Dashboard**: Verify agent settings in ElevenLabs console
5. **Test with Different Device**: Try on mobile or different computer

### Report the Issue:
If audio still doesn't work, provide:
- Browser and version
- Operating system
- Console logs during conversation
- Network tab showing WebSocket activity
- Audio device information
