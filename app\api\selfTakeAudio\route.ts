import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";
import { ref, uploadBytes, getDownloadURL, deleteObject, listAll, getMetadata } from "firebase/storage";
import { storage } from "../../../components/firebase";
import { v4 as uuidv4 } from "uuid";

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: "Authentication required",
        details: "Please sign in to record audio"
      }, { status: 401 });
    }

    const userId = session.user.email;

    // Process the audio file
    const formData = await req.formData();
    const audioFile = formData.get("audio") as File;
    const rehearsalId = formData.get("rehearsalId") as string;
    const scriptName = formData.get("scriptName") as string;

    if (!audioFile) {
      return NextResponse.json({
        success: false,
        error: "No audio file provided"
      }, { status: 400 });
    }

    //console.log(`[SelfTake API] Processing audio file: ${audioFile.name} (${audioFile.size} bytes) for user: ${userId}`);

    // Generate unique filename
    const timestamp = Date.now();
    const recordingId = uuidv4();
    const filename = `selftake-${rehearsalId || timestamp}.mp3`;

    // Upload to Firebase Storage
    const audioBuffer = Buffer.from(await audioFile.arrayBuffer());
    const storageRef = ref(storage, `${userId}/selfTake/${filename}`);

    await uploadBytes(storageRef, audioBuffer, {
      contentType: "audio/mp3",
      customMetadata: {
        rehearsalId: rehearsalId || `rehearsal-${timestamp}`,
        scriptName: scriptName || "Unknown Script",
        recordingId: recordingId,
        uploadedAt: new Date().toISOString()
      }
    });

    const audioUrl = await getDownloadURL(storageRef);

    //console.log("[SelfTake API] Audio uploaded successfully:", filename);

    return NextResponse.json({
      success: true,
      id: recordingId,
      filename: filename,
      url: audioUrl,
      timestamp: new Date().toISOString(),
      rehearsalId: rehearsalId || `rehearsal-${timestamp}`
    });

  } catch (error) {
    console.error("[SelfTake API] Error:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json({
      success: false,
      error: errorMessage,
      details: "Failed to process self-take recording"
    }, { status: 500 });
  }
}

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: "Authentication required"
      }, { status: 401 });
    }

    const userId = session.user.email;
    const { searchParams } = new URL(req.url);
    const requestedUserId = searchParams.get('userId');

    // Verify user can only access their own recordings
    if (requestedUserId !== userId) {
      return NextResponse.json({
        success: false,
        error: "Access denied"
      }, { status: 403 });
    }

    // List all recordings for the user
    const storageRef = ref(storage, `${userId}/selfTake/`);

    try {
      const listResult = await listAll(storageRef);
      const recordings = await Promise.all(
        listResult.items.map(async (itemRef) => {
          try {
            const url = await getDownloadURL(itemRef);
            const metadata = await getMetadata(itemRef);

            return {
              id: metadata.customMetadata?.recordingId || itemRef.name,
              filename: itemRef.name,
              url: url,
              timestamp: metadata.customMetadata?.uploadedAt || metadata.timeCreated,
              rehearsalId: metadata.customMetadata?.rehearsalId || 'unknown',
              scriptName: metadata.customMetadata?.scriptName || 'Unknown Script'
            };
          } catch (error) {
            console.error(`Error processing recording ${itemRef.name}:`, error);
            return null;
          }
        })
      );

      // Filter out failed recordings and sort by timestamp (newest first)
      const validRecordings = recordings
        .filter(recording => recording !== null)
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      return NextResponse.json({
        success: true,
        recordings: validRecordings
      });

    } catch (error) {
      // If the folder doesn't exist, return empty array
      if (error instanceof Error && error.message.includes('does not exist')) {
        return NextResponse.json({
          success: true,
          recordings: []
        });
      }
      throw error;
    }

  } catch (error) {
    console.error("[SelfTake API] GET Error:", error);
    return NextResponse.json({
      success: false,
      error: "Failed to load recordings"
    }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: "Authentication required"
      }, { status: 401 });
    }

    const userId = session.user.email;
    const { searchParams } = new URL(req.url);
    const recordingId = searchParams.get('id');
    const requestedUserId = searchParams.get('userId');

    // Verify user can only delete their own recordings
    if (requestedUserId !== userId) {
      return NextResponse.json({
        success: false,
        error: "Access denied"
      }, { status: 403 });
    }

    if (!recordingId) {
      return NextResponse.json({
        success: false,
        error: "Recording ID required"
      }, { status: 400 });
    }

    // List all recordings to find the one with matching ID
    const storageRef = ref(storage, `${userId}/selfTake/`);
    const listResult = await listAll(storageRef);

    let targetFile = null;
    for (const itemRef of listResult.items) {
      try {
        const metadata = await getMetadata(itemRef);
        if (metadata.customMetadata?.recordingId === recordingId || itemRef.name.includes(recordingId)) {
          targetFile = itemRef;
          break;
        }
      } catch (error) {
        console.error(`Error checking metadata for ${itemRef.name}:`, error);
      }
    }

    if (!targetFile) {
      return NextResponse.json({
        success: false,
        error: "Recording not found"
      }, { status: 404 });
    }

    // Delete the file
    await deleteObject(targetFile);

    //console.log(`[SelfTake API] Deleted recording: ${targetFile.name} for user: ${userId}`);

    return NextResponse.json({
      success: true,
      message: "Recording deleted successfully"
    });

  } catch (error) {
    console.error("[SelfTake API] DELETE Error:", error);
    return NextResponse.json({
      success: false,
      error: "Failed to delete recording"
    }, { status: 500 });
  }
}
