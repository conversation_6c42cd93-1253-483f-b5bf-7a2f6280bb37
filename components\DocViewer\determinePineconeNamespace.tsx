import { db } from 'components/firebase';
import { collection, query, where, getDocs, setDoc, doc } from 'firebase/firestore';

export async function determinePineconeNamespace(
    userId: string,
    category: string | null,
    uniqueFileId: string
): Promise<string> {
    let pineconeNamespace: string = uniqueFileId;

    if (category && category !== "Home") {
        // Access the category collection under the correct user path
        const categoryCollection = collection(db, "users", userId, "category");
        const categoryQuery = query(categoryCollection, where("Name", "==", category));

        const categorySnapshot = await getDocs(categoryQuery);

        if (!categorySnapshot.empty) {
            // Category exists, use its Namespace for Pinecone
            const categoryDoc = categorySnapshot.docs[0];
            pineconeNamespace = categoryDoc.data().Namespace;
            //console.log("Existing category found. Using Namespace for Pinecone:", pineconeNamespace);
        } else {
            // Category does not exist, assign the uniqueFileId as the new Namespace
            pineconeNamespace = uniqueFileId;
            //console.log("New category. Assigned uniqueFileId as the new Namespace for Pinecone:", pineconeNamespace);

            // Add new category record under the correct user path
            await setDoc(doc(db, "users", userId, "category", pineconeNamespace), {
                Name: category,
                Namespace: pineconeNamespace,
                createdAt: new Date(),
                Scenario: 'This is a test scenario'
            });
        }
    } else {
        // No category selected or "Home" category selected, use the unique file ID as the namespace
        //console.log("No category or 'Home' category selected. Created unique Namespace:", pineconeNamespace);
    }

    return pineconeNamespace;
}
