import { doc, getDoc } from "@firebase/firestore";
import { db } from "../components/firebase";

export async function fetchNamespaceForFile(fileDocumentId: string, userId: string): Promise<string | null> {
    try {
        //console.log(`Attempting to fetch namespace for fileDocumentId: ${fileDocumentId}, userId: ${userId}`);

        // Fetch the document from the 'files' collection
        const fileDocRef = doc(db, "users", userId, "files", fileDocumentId);
        const fileDoc = await getDoc(fileDocRef);

        if (fileDoc.exists()) {
            // Extract the namespace from the document data
            const data = fileDoc.data();
            const namespace = data?.namespace || null;

            if (namespace) {
                //console.log(`Namespace for fileDocumentId ${fileDocumentId}: ${namespace}`);
                return namespace;
            } else {
                console.warn(`Namespace not found in document for fileDocumentId ${fileDocumentId}`);
                return null;
            }
        } else {
            console.error(`Document with fileDocumentId ${fileDocumentId} does not exist.`);
            return null;
        }
    } catch (error) {
        console.error(`Error fetching namespace for fileDocumentId ${fileDocumentId}:`, error);
        throw new Error(`Unable to fetch namespace for fileDocumentId ${fileDocumentId}`);
    }
}
