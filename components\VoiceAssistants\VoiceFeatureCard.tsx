import React, { useState, useEffect } from 'react';
import { Mi<PERSON>, MicOff, Volume2, VolumeX } from 'lucide-react';
import { useConversation } from "@elevenlabs/react";

const VoiceFeatureCard = () => {
  const [hasPermission, setHasPermission] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isListening, setIsListening] = useState(false);

  const conversation = useConversation({
    onConnect: () => {
      //console.log("Connected to ElevenLabs");
    },
    onDisconnect: () => {
      //console.log("Disconnected from ElevenLabs");
    },
    onMessage: (message) => {
      if (typeof message === "string") {
        //console.log("Voice input:", message);
      } else if (message && typeof message === "object" && 'message' in message) {
        //console.log("AI response:", message.message);
      }
    },
  });

  const { status, isSpeaking } = conversation;

  useEffect(() => {
    const requestMicPermission = async () => {
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true });
        setHasPermission(true);
      } catch (error) {
        setErrorMessage("Microphone access denied");
        console.error("Error accessing microphone:", error);
      }
    };

    requestMicPermission();
  }, []);

  const handleStartConversation = async () => {
    try {
      setIsListening(true);
      const conversationId = await conversation.startSession({
        agentId: process.env.NEXT_PUBLIC_ELEVENLABS_AGENT_ID!,
      });
      //console.log("Started conversation:", conversationId);
    } catch (error) {
      setErrorMessage("Failed to start conversation");
      console.error("Error starting conversation:", error);
    }
  };

  const handleEndConversation = async () => {
    try {
      setIsListening(false);
      await conversation.endSession();
    } catch (error) {
      setErrorMessage("Failed to end conversation");
      console.error("Error ending conversation:", error);
    }
  };

  const toggleMute = async () => {
    try {
      await conversation.setVolume({ volume: isMuted ? 1 : 0 });
      setIsMuted(!isMuted);
    } catch (error) {
      setErrorMessage("Failed to change volume");
      console.error("Error changing volume:", error);
    }
  };

  return (
    <div className="bg-gray-200 text-ike-purple_b shadow-ike-purple_b rounded-lg shadow-lg overflow-hidden">
      <div className="p-6">
        <div className="flex items-center justify-center mb-4">
          <Mic className="w-12 h-12 text-amber-600" />
          <h3 className="text-2xl font-semibold ml-4">Voice Assistant</h3>
        </div>
        <p className="text-gray-600 mb-6 text-center">
          Experience hands-free interaction with iKe using our voice-enabled assistant. Ask questions, get instant responses, and navigate through features effortlessly.
        </p>

        <div className="flex flex-col items-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <button
              onClick={toggleMute}
              disabled={status !== "connected"}
              className={`p-2 rounded-full ${
                status === "connected"
                  ? "bg-amber-600 hover:bg-amber-700"
                  : "bg-gray-600"
              } transition-colors`}
              title={isMuted ? "Unmute" : "Mute"}
            >
              {isMuted ? (
                <VolumeX className="h-4 w-4 text-white" />
              ) : (
                <Volume2 className="h-4 w-4 text-white" />
              )}
            </button>

            {status === "connected" ? (
              <button
                onClick={handleEndConversation}
                className="flex items-center gap-2 px-4 py-2 mb-2 bg-red-600 hover:bg-red-700 text-white rounded-full transition-colors"
              >
                <MicOff className="h-4 w-4" />
                Stop Listening
              </button>
            ) : (
              <button
                onClick={handleStartConversation}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-full transition-colors"
                disabled={!hasPermission}
              >
                <Mic className="h-4 w-4" />
                Ask a question
              </button>
            )}
          </div>

          {isListening && (
            <div className="relative">
              <div className={`w-12 h-12 rounded-full bg-ike-purple ${
                isSpeaking ? 'animate-pulse' : ''
              } flex items-center justify-center`}>
                <Mic className="h-6 w-6 text-white mt-2" />
              </div>
              <div className="absolute inset-0 w-12 h-12 rounded-full bg-amber-500 opacity-75 animate-ping" />
            </div>
          )}

          {status === "connected" && (
            <div className="text-sm">
              {isSpeaking ? (
                <span className="text-green-500">Speaking...</span>
              ) : isListening ? (
                <span className="text-amber-500">Listening...</span>
              ) : null}
            </div>
          )}

          {errorMessage && (
            <div className="text-sm text-red-500">{errorMessage}</div>
          )}

          {!hasPermission && (
            <div className="text-sm text-yellow-500">
              Please allow microphone access
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VoiceFeatureCard;