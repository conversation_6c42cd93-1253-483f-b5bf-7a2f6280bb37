// src/agents/ProcessGroqMessagesAgent.ts

import { ProcessGroqMessagesTool } from "components/tools/ProcessGroqMessagesTool";
import { VisualizationAgent } from "./VisualizationAgent";
import { StreamToClientTool } from "components/tools/StreamToClientTool";
import { ProcessMessageInput, ProcessMessageOutput } from "@/src/types/shared";
import { VisualizationRequestAnalyzerTool } from "components/tools/VisualizationRequestAnalyzerTool";
import { LLMDataProcessingTool } from "components/tools/LLMDataProcessingTool";
import { EnhancedVisualizationProcessor } from "components/tools/EnhancedVisualizationProcessor";
import { VisualizationAnalyzerTool } from "components/tools/VisualizationAnalyzerTool";
import { HumanMessage, BaseMessage } from "@langchain/core/messages";
import { VisualizationData } from "@/src/types/VisualizationTypes";

export class ProcessGroqMessagesAgent {
  private readonly groqTool: ProcessGroqMessagesTool;
  private readonly visualizationAnalyzer: VisualizationRequestAnalyzerTool;
  private readonly llmDataProcessor: LLMDataProcessingTool;
  private readonly visualizationProcessor: EnhancedVisualizationProcessor;
  private readonly visualizationAnalyzerTool: VisualizationAnalyzerTool;
  private readonly defaultColors = ['#4299E1', '#48BB78', '#ED8936', '#667EEA'];

  constructor(
    private readonly streamTool: StreamToClientTool,
    public readonly visualizationAgent?: VisualizationAgent,
    apiKey?: string
  ) {
    //console.log("ProcessGroqMessagesAgent: Initializing with enhanced processing");

    this.groqTool = new ProcessGroqMessagesTool(streamTool);

    const GroqKey = apiKey || process.env.GROQ_API_KEY!;
    this.llmDataProcessor = new LLMDataProcessingTool(GroqKey, { maxRetries: 3 });
    this.visualizationAnalyzer = new VisualizationRequestAnalyzerTool(this.llmDataProcessor);
    this.visualizationAnalyzerTool = new VisualizationAnalyzerTool();

    this.visualizationProcessor = new EnhancedVisualizationProcessor(
      this.llmDataProcessor,
      this.visualizationAnalyzer,
      this.visualizationAnalyzerTool
    );

    //console.log("ProcessGroqMessagesAgent: Enhanced initialization complete");
  }

  private getUserMessageContent(messages: BaseMessage[]): string {
    const userMessage = messages.find((msg): msg is HumanMessage => msg instanceof HumanMessage);
    if (!userMessage) {
      throw new Error("No user message found in prompt");
    }

    return typeof userMessage.content === 'string' ? userMessage.content : JSON.stringify(userMessage.content);
  }

  async processMessage(input: ProcessMessageInput): Promise<ProcessMessageOutput> {
    //console.log("ProcessGroqMessagesAgent: Starting enhanced message analysis");

    try {
      const messages = await input.prompt.toChatMessages();
      //console.log("ProcessGroqMessagesAgent: Extracted chat messages:", messages);

      const userQuery = this.getUserMessageContent(messages);
      //console.log("ProcessGroqMessagesAgent: Extracted user query:", userQuery);

      const analysis = await this.visualizationAnalyzer.call(userQuery);
      //console.log("ProcessGroqMessagesAgent: Initial analysis complete:", analysis);
      //console.log("ProcessGroqMessagesAgent: User query being analyzed:", userQuery);

      if (analysis.isVisualizationRequest && analysis.confidence >= 0.7) {
        //console.log("ProcessGroqMessagesAgent: Routing to enhanced visualization flow with user query");
        return await this.handleEnhancedVisualizationFlow(input, analysis, userQuery);
      } else {
        //console.log("ProcessGroqMessagesAgent: Routing to regular message flow");
        return await this.handleRegularFlow(input);
      }

    } catch (error) {
      console.error("ProcessGroqMessagesAgent: Error during processing:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async handleEnhancedVisualizationFlow(
    input: ProcessMessageInput,
    analysis: any,
    userQuery: string
  ): Promise<ProcessMessageOutput> {
    //console.log("ProcessGroqMessagesAgent: Enhanced Visualization Flow: Starting processing");
    //console.log("ProcessGroqMessagesAgent: Enhanced Visualization Flow: Using user query:", userQuery);
    
    try {
      // Start processing stream
      await this.streamTool.startProcessing(input.streamOptions);
      //console.log("ProcessGroqMessagesAgent: Enhanced Visualization Flow: Started processing stream");

      // Process content based on user query
      const processedContent = this.visualizationProcessor.processContent(userQuery);
      //console.log("ProcessGroqMessagesAgent: Enhanced Visualization Flow: Content processing complete:", processedContent);

      // Interact with the language model to get processed data
      const processedData = await this.llmDataProcessor.call({
        content: processedContent.request + (processedContent.data ? `\n\n${processedContent.data}` : ''),
        options: {
          includeTimeAnalysis: true,
          includeSuggestions: true
        }
      });
      //console.log("ProcessGroqMessagesAgent: Enhanced Visualization Flow: LLM data processing complete:", processedData);

      if (!processedData.data || processedData.data.length === 0) {
        throw new Error("ProcessGroqMessagesAgent: Enhanced Visualization Flow: No valid data points found for visualization");
      }

      // Determine the chart type based on analysis suggestions
      let suggestedType = analysis.suggestion?.type || 'bar';
      const typeMapping: Record<string, string> = {
        'stacked-column': 'stacked-bar',
        'stacked-bar': 'stacked-bar',
        'bar': 'bar',
        'line': 'line',
        'pie': 'pie',
        'scatter': 'scatter',
        'tree': 'tree',
        'flow': 'flow',
        'composed': 'composed',
        'flow-diagram': 'flow-diagram'
      };
      const chartType = typeMapping[suggestedType] || 'bar';
      const isStacked = (chartType === 'stacked-bar');

      // Construct the visualization object adhering to the agreed JSON structure
      const visualization: VisualizationData = {
        charttype: "visualization",
        content: {
          type: "chart",
          config: {
            type: chartType,
            stacked: isStacked,
            xAxis: processedData.metadata.timeUnit ? "time" : "category",
            yAxis: processedData.metadata.metrics[0],
            series: processedData.metadata.metrics,
            colors: this.defaultColors.slice(0, processedData.metadata.metrics.length)
          },
          data: processedData.data,
          metadata: {
            type: processedData.metadata.type,
            metrics: processedData.metadata.metrics,
            timeUnit: processedData.metadata.timeUnit ?? undefined
          },
          confidence: analysis.confidence,
          reasoning: analysis.suggestion?.reason || ""
          // 'url' will be added by SupervisorAgent
        }
      };

      //console.log("ProcessGroqMessagesAgent: Enhanced Visualization Flow: Constructed visualization object:", JSON.stringify(visualization, null, 2));

      // **Embed Visualization JSON within the AI message's text field wrapped in ```json``` code block**
      const visualizationJson = `\n\n\`\`\`json\n${JSON.stringify(visualization, null, 2)}\n\`\`\``;

      // Enqueue the visualization JSON as part of the AI message's text
      await this.streamTool.streamToken(
        input.streamOptions,
        visualizationJson
      );

      //console.log("ProcessGroqMessagesAgent: Enhanced Visualization Flow: Embedded Visualization JSON into AI message text");

      // Finish processing stream
      // await this.streamTool.finishProcessing(input.streamOptions);
      // //console.log("ProcessGroqMessagesAgent: Enhanced Visualization Flow: Finished processing stream");

      return {
        success: true,
        visualization: {
          charttype: "visualization", // This is the top-level type indicating the object is a visualization
          type: visualization.content.type, // Add the 'type' property
          config: visualization.content.config, // Add the 'config' property
          data: visualization.content.data, // Add the 'data' property
          content: {
            type: visualization.content.type, // This should be "chart" or "table"
            config: visualization.content.config,
            data: visualization.content.data,
            metadata: visualization.content.metadata,
            confidence: visualization.content.confidence,
            reasoning: analysis.suggestion?.reason || "", // Ensure reasoning is an empty string if undefined
            // Include the 'url' if it's part of the content
            url: visualization.content.url
          }
        }
      };

    } catch (error: any) {
      console.error("ProcessGroqMessagesAgent: Enhanced Visualization Flow: Processing failed:", error);
      // Stream the error to the client
      await this.streamTool.streamError(
        input.streamOptions,
        error instanceof Error ? error : new Error(String(error))
      );
      // Finish processing stream even if there was an error
      // await this.streamTool.finishProcessing(input.streamOptions);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async handleRegularFlow(input: ProcessMessageInput): Promise<ProcessMessageOutput> {
    //console.log("ProcessGroqMessagesAgent: Regular Flow: Processing through ProcessGroqMessagesTool");

    try {
      // Delegate to the groqTool to handle regular message processing
      const result = await this.groqTool._call({
        userEmail: input.userEmail,
        streamOptions: input.streamOptions,
        prompt: input.prompt,
        config: {
          temperature: input.config?.temperature ?? 0.3,
          maxTokens: input.config?.maxTokens ?? 5000
        }
      });

      if (!result.success) {
        console.error("ProcessGroqMessagesAgent: Regular Flow: Error occurred during regular message processing:", result.error);
        return {
          success: false,
          error: result.error || "ProcessGroqMessagesAgent: Regular Flow: Regular message processing failed"
        };
      }
      
      //console.log("ProcessGroqMessagesAgent: Regular Flow: Successfully processed regular message");

      // Stream the regular response to the client
      await this.streamTool.streamToken(input.streamOptions, result.response.generations[0][0].text);

      // Finish processing stream
      await this.streamTool.finishProcessing(input.streamOptions);
      //console.log("ProcessGroqMessagesAgent: Regular Flow: Finished processing stream");

      return {
        success: true,
        response: result.response
      };
    } catch (error: any) {
      console.error("ProcessGroqMessagesAgent: Regular Flow: Error during regular message processing:", error);
      // Stream the error to the client
      await this.streamTool.streamError(
        input.streamOptions,
        error instanceof Error ? error : new Error(String(error))
      );
      // Finish processing stream even if there was an error
      await this.streamTool.finishProcessing(input.streamOptions);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}
