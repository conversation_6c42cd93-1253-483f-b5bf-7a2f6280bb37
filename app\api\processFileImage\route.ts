import { NextRequest, NextResponse } from "next/server";
import { setDoc, doc as firestoreDoc } from "firebase/firestore";
import { db } from "../../../components/firebase";
import { OpenAIEmbeddings } from "@langchain/openai";
import { Pinecone } from "@pinecone-database/pinecone";
import { FirestoreStore } from "lib/FirestoreStore";
import { processDocument } from "components/DocViewer/documentProcessors";
import { createGroqClient } from "lib/llms/groq";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";

// Model constant
const GROQ_VISION_MODEL = process.env.GROQ_VISION_MODEL || "llama-3.2-90b-vision-preview";

interface Document {
  pageContent: string;
  metadata: Record<string, any>;
}

function cleanMetadata(metadata: any): Record<string, any> {
  const cleaned: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(metadata)) {
    if (value !== undefined && value !== null) {
      cleaned[key] = value;
    } else {
      switch (key) {
        case 'sectionTitle':
          cleaned[key] = 'No Title';
          break;
        case 'questions':
          cleaned[key] = [];
          break;
        case 'is_summary':
          cleaned[key] = false;
          break;
        case 'content':
          cleaned[key] = '';
          break;
        default:
          cleaned[key] = '';
      }
    }
  }
  
  return cleaned;
}

function createErrorMetadata(error: any, docInfo: {
  docId: string;
  fileName: string;
  fileType: string;
  category?: string;
  chunkId?: string;
}): Record<string, any> {
  return cleanMetadata({
    documentId: docInfo.docId,
    document_title: docInfo.fileName,
    file_type: docInfo.fileType,
    category: docInfo.category || 'Uncategorized',
    chunk_id: docInfo.chunkId || docInfo.docId,
    error_message: error instanceof Error ? error.message : 'Unknown error',
    error_details: error.toString(),
    created_at: new Date().toISOString(),
    sectionTitle: 'Processing Error',
    status: 'failed',
    is_error: true,
    processing_stage: 'document_processing'
  });
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  // Get server session and validate
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.email) {
    return NextResponse.json(
      { success: false, error: "Unauthorized - No valid session" },
      { status: 401 }
    );
  }

  // Initialize Groq client with session user's email
  const groq = createGroqClient({ userEmail: session.user.email });

  // Define processImageWithGroq with access to groq client
  const processImageWithGroq = async (imageUrl: string): Promise<{ analysis: string; sectionTitle: string }> => {
    try {
      const chatCompletion = await groq.chat.completions.create({
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `IMAGE TEXT CAPTURE:

MAIN TEXT:
[Verbatim capture of headlines, titles, main body text]

CHART/GRAPH TEXT:
- Axes labels: [x-axis, y-axis labels]
- Values/Data points: [numerical values, labels]
- Legend text: [categories, keys]
- Annotations: [notes, callouts]

SUPPLEMENTARY TEXT:
- Captions/Subtitles: [verbatim]
- Watermarks/Credits: [verbatim]
- UI elements: [buttons, menu items]

TITLE: (2-3 words capturing essence)

CORE ELEMENTS:
- Composition: Layout, focal points
- Visuals: Colors, lighting, quality
- Content: Main subjects, setting
- Impact: Mood, context

If people present:
- Appearance, expressions, interactions

TEXT PROPERTIES:
- Typography styles/hierarchy
- Text placement/flow
- Legibility/contrast`
              },
              {
                type: "image_url",
                image_url: {
                  url: imageUrl
                }
              }
            ]
          }
        ],
        model: GROQ_VISION_MODEL,
        temperature: 0.7,
        max_tokens: 1024,
      });

      if (!chatCompletion?.choices?.[0]?.message?.content) {
        throw new Error("Invalid response format from Groq API");
      }

      const response = chatCompletion.choices[0].message.content;
      
      const titleMatch = response.match(/TITLE:\s*(.*?)\s*\n/i);
      const analysisMatch = response.match(/ANALYSIS:\s*([\s\S]*)/i);

      const sectionTitle = titleMatch?.[1] || "Image Analysis";
      const analysis = analysisMatch?.[1]?.trim() || response.trim();

      return {
        analysis,
        sectionTitle
      };
    } catch (error) {
      console.error("Error processing image with Groq:", error);
      if (error instanceof Error) {
        throw new Error(`Image processing failed: ${error.message}`);
      }
      throw new Error("Image processing failed with unknown error");
    }
  };

  let docId = '';
  let userId = '';
  let fileName = '';
  let fileType = '';
  let category = 'Uncategorized';
  let fileUrl = '';
  let isImage = false;

  try {
    const body = await req.json();
    ({ docId, userId, category = "Uncategorized", fileName, fileType, fileUrl, isImage } = body);

    if (!docId || !userId || !fileName || !fileType || !fileUrl) {
      throw new Error("Missing required parameters");
    }

    const embeddings = new OpenAIEmbeddings({
      apiKey: process.env.OPENAI_API_KEY!,
    });
    const pinecone = new Pinecone();
    const pineconeIndex = pinecone.Index(process.env.PINECONE_INDEX!);
    const byteCollection = `users/${userId}/byteStoreCollection`;
    const firestoreStore = new FirestoreStore({ collectionPath: byteCollection });

    let processedContent;
    try {
      if (isImage) {
        const { analysis, sectionTitle } = await processImageWithGroq(fileUrl);
        processedContent = [{
          pageContent: analysis,
          metadata: {
            doc_id: docId,
            chunk_id: `${docId}_1`,
            sectionTitle: sectionTitle,
            questions: [],
            is_summary: true
          }
        }];
      } else {
        const fileResponse = await fetch(fileUrl);
        if (!fileResponse.ok) {
          throw new Error(`Failed to fetch file: ${fileResponse.statusText}`);
        }
        
        const fileBlob = await fileResponse.blob();
        const file = new File([fileBlob], fileName, { type: fileType });
        // Assuming the missing arguments are userId, category, and an additional parameter not specified in the context
        // Adding placeholders for the two known missing arguments and a generic 'additionalParameter' for the unspecified one
        const additionalParameter = ''; // Placeholder for the actual additional parameter needed
        const CHUNK_SIZE = 1500; // Example value, adjust as needed
        const CHUNK_OVERLAP = 200; // Example value, adjust as needed
        processedContent = await processDocument(file, docId, fileType, fileName, userId, category, additionalParameter, CHUNK_SIZE, CHUNK_OVERLAP);
      }

      if (!processedContent?.length) {
        throw new Error('No content was extracted from the document');
      }

      // Store all chunks in byteStoreCollection
      const byteStoreData: [string, Document][] = processedContent.map(doc => [
        doc.metadata.chunk_id,
        {
          pageContent: doc.pageContent,
          metadata: cleanMetadata({
            ...doc.metadata,
            document_title: fileName,
            category: category,
            file_type: fileType,
            is_image: isImage,
            fileUrl: fileUrl,
            processed_at: new Date().toISOString()
          })
        }
      ]);

      await firestoreStore.mset(byteStoreData);

      // Process and embed each chunk
      for (const doc of processedContent) {
        try {
          const embedding = await embeddings.embedQuery(doc.pageContent);

          const metadataForPinecone = cleanMetadata({
            content: doc.pageContent,
            doc_id: doc.metadata.doc_id,
            chunk_id: doc.metadata.chunk_id,
            document_title: fileName,
            category: category,
            file_type: fileType,
            is_image: isImage,
            sectionTitle: doc.metadata.sectionTitle,
            questions: doc.metadata.questions,
            is_summary: doc.metadata.is_summary
          });

          await pineconeIndex.namespace(docId).upsert([
            {
              id: doc.metadata.chunk_id,
              values: embedding,
              metadata: metadataForPinecone,
            },
          ]);

          //console.log(`Successfully processed chunk: ${doc.metadata.chunk_id}`);
        } catch (chunkError: any) {
          console.error(`Error processing chunk ${doc.metadata.chunk_id}:`, chunkError);
          
          const errorMetadata = createErrorMetadata(chunkError, {
            docId,
            fileName,
            fileType,
            category,
            chunkId: doc.metadata.chunk_id
          });

          await setDoc(
            firestoreDoc(db, "users", userId, "MetadataFallback", doc.metadata.chunk_id),
            errorMetadata
          );
        }
      }

      return NextResponse.json({ success: true });
      
    } catch (processingError: any) {
      console.error("Error in document processing:", processingError);
      
      const errorMetadata = createErrorMetadata(processingError, {
        docId,
        fileName,
        fileType,
        category
      });

      await setDoc(
        firestoreDoc(db, "users", userId, "MetadataFallback", docId),
        errorMetadata
      );

      throw processingError;
    }
    
  } catch (error: any) {
    console.error("Error processing file:", error);
    
    if (docId && userId) {
      try {
        const errorMetadata = createErrorMetadata(error, {
          docId,
          fileName: fileName || 'Unknown File',
          fileType: fileType || 'Unknown Type',
          category
        });

        await setDoc(
          firestoreDoc(db, "users", userId, "MetadataFallback", docId),
          errorMetadata
        );
      } catch (metadataError) {
        console.error("Failed to save error metadata:", metadataError);
      }
    }

    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error occurred" 
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS(req: NextRequest): Promise<NextResponse> {
  return NextResponse.json({}, { status: 200 });
}