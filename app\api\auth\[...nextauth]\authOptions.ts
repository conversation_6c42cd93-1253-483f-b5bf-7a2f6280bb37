import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import { NextAuthOptions } from "next-auth";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    // You can add more providers here
  ],
  secret: process.env.NEXTAUTH_SECRET, // Ensure this is set in your .env file
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
};

// Export GET and POST handlers for NextAuth
const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };




