// createLogicalSchema.ts
import { API_CONFIG } from './config';

// Function to clean the API response before parsing it
function cleanAPIResponse(responseText: string): string {
  try {
    // Step 1: Remove backticks and markdown code block markers
    let cleanedText = responseText.replace(/```/g, '').trim();

    // Step 2: Validate if it's JSON by ensuring it starts with '{' or '['
    if (cleanedText.startsWith('{') || cleanedText.startsWith('[')) {
      return cleanedText; // It's clean JSON, ready for parsing
    } else {
      throw new Error("Cleaned text does not appear to be valid JSON.");
    }
  } catch (error) {
    console.error("Error cleaning API response:", error);
    throw new Error("Failed to clean and prepare the response for JSON parsing.");
  }
}

// Main function to interact with the API
export async function createLogicalSchema(data: string) {
  try {
    // Prepare the payload to be sent to the API
    const payload = { question: data };

    const response = await fetch(
      API_CONFIG.endpoint || "https://flowiseai-railway-production-28a57.up.railway.app/api/v1/prediction/************************************",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(payload) // Send the payload as JSON
      }
    );

    // Check if the response is OK
    if (!response.ok) {
      throw new Error(`Error: ${response.status}`);
    }

    // Fetch the raw text from the response
    const responseText = await response.text();
    //console.log('Raw API Response Text:', responseText);

    // Clean the response text to remove unwanted characters before parsing
    const cleanedResponse = cleanAPIResponse(responseText);
    //console.log('Cleaned API Response:', cleanedResponse);

    // Parse the cleaned response as JSON
    const jsonResponse = JSON.parse(cleanedResponse);
    //console.log('Parsed JSON Response:', jsonResponse);

    return jsonResponse;
  } catch (error) {
    console.error('Error in createLogicalSchema:', error);
    return null; // Return null or an empty object in case of error
  }
}
