import { Pinecone } from '@pinecone-database/pinecone';

export async function searchSpecificNamespaces(
  pineconeIndexName: string, 
  queryVector: number[], 
  topK: number, 
  namespaces: string[]
) {
  const pinecone = new Pinecone();
  const index = pinecone.index(process.env.PINECONE_INDEX!);

  let allMatches: any[] = [];

  for (const namespace of namespaces) {
    try {
      // Query the specified namespace
      const queryResponse = await index.namespace(namespace).query({
        topK: topK,
        vector: queryVector,
        includeValues: true,
      });

      //console.log(`Results from namespace ${namespace}:`, queryResponse);

      // Append the matches from each namespace and include the namespace info
      if (queryResponse.matches) {
        allMatches = allMatches.concat(queryResponse.matches.map(match => ({
          ...match,
          namespace,  // Track the namespace each result is from
        })));
      }
    } catch (error) {
      console.error(`Error querying namespace ${namespace}:`, error);
    }
  }

  // Return the combined matches from the specified namespaces
  return allMatches;
}
