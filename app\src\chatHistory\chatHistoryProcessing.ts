/**
 * chatHistoryProcessing.ts
 *
 * This module handles the processing and analysis of chat history data.
 * It converts raw chat history into a structured format and performs token counting
 * for AI context management. The module enforces limits on history size to prevent
 * token usage from exceeding model constraints.
 *
 * Key features:
 * - Converts raw chat history to structured message array
 * - Enforces message count and token limits
 * - Provides analytics on history token usage
 */

import { ChatHistoryProcessor, ChatMessage, ChatHistoryAnalytics, ChatHistoryConversionMetrics } from '../../../lib/ChatHistoryProcessor';

// Extended interface to ensure type safety for processor methods
interface ExtendedChatHistoryProcessor extends ChatHistoryProcessor {
  convertToMessageArray(rawHistory: string): {
    messages: ChatMessage[];
    metrics: ChatHistoryConversionMetrics;
  };
  calculateHistoryTokens(chatHistory: string): {
    tokenCount: number;
    analytics: ChatHistoryAnalytics;
  };
}

// Maximum number of previous messages to include in history
const MAX_HISTORY_MESSAGES = 10;

// Maximum tokens allowed per individual message
const MAX_TOKENS_PER_MESSAGE = 500;

/**
 * Escapes template literal characters in code snippets
 * @param text - Text that may contain code snippets
 * @returns Escaped text safe for template processing
 */
function escapeCodeSnippets(text: string): string {
  // First identify code blocks (both ``` and single `)
  const codeBlockRegex = /```[\s\S]*?```|`[^`\n]+`/g;

  return text.replace(codeBlockRegex, (match) => {
    // Escape curly braces in code blocks
    return match.replace(/{/g, '{{').replace(/}/g, '}}');
  });
}

/**
 * Processes raw chat history into a structured format with token analytics
 *
 * @param rawChatHistory - String containing the raw chat history data
 * @returns Object containing processed chat data
 */
export function processChatHistory(rawChatHistory: string): {
  chatHistoryArray: ChatMessage[];
  chatHistoryTokens: number;
  historyAnalytics: ChatHistoryAnalytics;
} {
  // Initialize processor with configuration limits
  const historyProcessor = new ChatHistoryProcessor({
    maxHistoryMessages: MAX_HISTORY_MESSAGES,
    maxTokensPerMessage: MAX_TOKENS_PER_MESSAGE
  });

  // Escape code snippets in the raw history before processing
  const escapedHistory = escapeCodeSnippets(rawChatHistory);

  // Convert escaped history string into structured message array
  const conversionResult = (historyProcessor as ExtendedChatHistoryProcessor).convertToMessageArray(escapedHistory);
  const rawChatHistoryArray = conversionResult.messages;

  // Process each message to ensure code snippets are properly escaped
  const chatHistoryArray = rawChatHistoryArray.map((msg: ChatMessage) => ({
    ...msg,
    text: escapeCodeSnippets(msg.text)
  }));

  // Calculate token usage and generate analytics for the history
  const { tokenCount: chatHistoryTokens, analytics: historyAnalytics } =
    (historyProcessor as ExtendedChatHistoryProcessor).calculateHistoryTokens(escapedHistory);

  return {
    chatHistoryArray,   // Processed messages array with escaped code
    chatHistoryTokens,  // Total tokens in history
    historyAnalytics    // Additional metrics about history usage
  };
}