"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Upload, Bot, Voicemail, Camera, BrainCircuit, ScrollText, Star, Users, Clock } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { signOut, useSession, signIn } from "next-auth/react"
import { useRouter } from "next/navigation"
import { doc, getDoc } from "firebase/firestore"
import { db } from "components/firebase"
import Readermodal from "components/scriptreaderAI/Reader-modal"
import UserDetailsModal from "components/UserDetailsModal"
import LoadingOverlay from "components/LoadingOverlay"
import { CompactThemeToggle } from "components/ThemeToggle"

// The SVG pattern component for the background
const WavePattern = ({ className }: { className: string }) => (
  <svg width="100%" height="100%" viewBox="0 0 1000 1000" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg">
    <g className={className}>
      <path
        d="M -1000 100 C -750 0, -750 200, -500 100 S -250 0, 0 100 S 250 200, 500 100 S 750 0, 1000 100 S 1250 200, 1500 100 S 1750 0, 2000 100"
        fill="none"
        stroke="currentColor"
        strokeWidth="0.8"
      />
      <path
        d="M -1000 300 C -750 200, -750 400, -500 300 S -250 200, 0 300 S 250 400, 500 300 S 750 200, 1000 300 S 1250 400, 1500 300 S 1750 200, 2000 300"
        fill="none"
        stroke="currentColor"
        strokeWidth="0.8"
      />
      <path
        d="M -1000 500 C -750 400, -750 600, -500 500 S -250 400, 0 500 S 250 600, 500 500 S 750 400, 1000 500 S 1250 600, 1500 500 S 1750 400, 2000 500"
        fill="none"
        stroke="currentColor"
        strokeWidth="0.8"
      />
      <path
        d="M -1000 700 C -750 600, -750 800, -500 700 S -250 600, 0 700 S 250 800, 500 700 S 750 600, 1000 700 S 1250 800, 1500 700 S 1750 600, 2000 700"
        fill="none"
        stroke="currentColor"
        strokeWidth="0.8"
      />
      <path
        d="M -1000 900 C -750 800, -750 1000, -500 900 S -250 800, 0 900 S 250 1000, 500 900 S 750 800, 1000 900 S 1250 1000, 1500 900 S 1750 800, 2000 900"
        fill="none"
        stroke="currentColor"
        strokeWidth="0.8"
      />
    </g>
  </svg>
)

// Enhanced AnimatedWavyBackground with more visible animation
const AnimatedWavyBackground = () => (
  <>
    <style>
      {`
        @keyframes wave-motion {
          0% { transform: translateX(0); }
          100% { transform: translateX(-70%); }
        }
        @keyframes wave-motion-reverse {
          0% { transform: translateX(-70%); }
          100% { transform: translateX(0); }
        }
      `}
    </style>
    <div className="fixed inset-0 z-0 overflow-hidden pointer-events-none">
      {/* First wave layer */}
      <div
        className="absolute top-0 left-0 w-[200%] h-full flex"
        style={{ animation: "wave-motion 60s linear infinite" }}
      >
        <div className="w-1/2 h-full">
          <WavePattern className="text-emerald-400/30 dark:text-emerald-400/25" />
        </div>
        <div className="w-1/2 h-full">
          <WavePattern className="text-emerald-400/30 dark:text-emerald-400/25" />
        </div>
      </div>
      {/* Second wave layer */}
      <div
        className="absolute top-0 left-0 w-[200%] h-full flex opacity-80"
        style={{ animation: "wave-motion-reverse 80s linear infinite" }}
      >
        <div className="w-1/2 h-full">
          <WavePattern className="text-emerald-500/25 dark:text-emerald-500/20" />
        </div>
        <div className="w-1/2 h-full">
          <WavePattern className="text-emerald-500/25 dark:text-emerald-500/20" />
        </div>
      </div>
      {/* Third wave layer */}
      <div
        className="absolute top-0 left-0 w-[200%] h-full flex opacity-60"
        style={{ animation: "wave-motion 100s linear infinite" }}
      >
        <div className="w-1/2 h-full">
          <WavePattern className="text-emerald-600/20 dark:text-emerald-600/15" />
        </div>
        <div className="w-1/2 h-full">
          <WavePattern className="text-emerald-600/20 dark:text-emerald-600/15" />
        </div>
      </div>
    </div>
  </>
)

export default function AiScriptReaderPage() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [hasLoaded, setHasLoaded] = useState(false)
  const { data: session, status, update } = useSession()
  const router = useRouter()
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const [showUserDetailsModal, setShowUserDetailsModal] = useState(false)
  const [isProfileIncomplete, setIsProfileIncomplete] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isSigningOut, setIsSigningOut] = useState(false)
  const [hasCheckedProfile, setHasCheckedProfile] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => {
      setHasLoaded(true)
    }, 500)
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    async function checkUserAccount() {
      if (status === "authenticated" && session?.user?.email && !hasCheckedProfile) {
        const accountDoc = await getDoc(doc(db, "Accounts", session.user.email))
        if (!accountDoc.exists()) {
          setShowUserDetailsModal(true)
          setIsProfileIncomplete(true)
        } else {
          setIsProfileIncomplete(false)
        }
        setHasCheckedProfile(true)
      }
    }
    checkUserAccount()
  }, [status, session, hasCheckedProfile])

  useEffect(() => {
    if (status === "unauthenticated") {
      setIsUserMenuOpen(false)
      setShowUserDetailsModal(false)
      setIsProfileIncomplete(false)
      setIsSigningOut(false)
      setIsLoading(false)
      setHasCheckedProfile(false)
    }
  }, [status])

  const handleOpenModal = () => {
    if (status === "authenticated" && session?.user && !isSigningOut) {
      setIsModalOpen(true)
    } else {
      handleSignIn()
    }
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
  }

  const handleSignIn = async () => {
    try {
      setIsLoading(true)
      await signIn("google", { callbackUrl: "/" })
    } catch (error) {
      console.error("Sign in error:", error)
      setIsLoading(false)
    }
  }

  const handleSignOut = async () => {
    setIsUserMenuOpen(false)
    setIsLoading(true)
    setIsSigningOut(true)
    try {
      await signOut({ redirect: false })
      await update()
      // Reset all state on sign out
      setIsUserMenuOpen(false)
      setShowUserDetailsModal(false)
      setIsProfileIncomplete(false)
      setHasCheckedProfile(false)
      router.push("/")
      router.refresh()
    } catch (error) {
      console.error("Sign out error:", error)
    } finally {
      setIsLoading(false)
      setIsSigningOut(false)
    }
  }

  const handleUserDetailsComplete = () => {
    setShowUserDetailsModal(false)
    setIsProfileIncomplete(false)
    setIsLoading(true)
    router.push("/")
  }

  const handleUserDetailsClose = () => {
    setShowUserDetailsModal(false)
  }

  const showSignInButton = status === "unauthenticated" || (isSigningOut && !session)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.15 } },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
  }

  const features = [
    {
      icon: <Upload className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />,
      title: "Effortless Script Upload",
      description: "Simply upload your script in various formats and let CastMate instantly prepare it for rehearsal.",
    },
    {
      icon: <Bot className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />,
      title: "AI Scene Partner",
      description: "Rehearse with an AI that delivers lines with emotion and timing, adapting to your performance.",
    },
    {
      icon: <Voicemail className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />,
      title: "Diverse Voice Options",
      description: "Choose from a library of voices and assign them to different characters for a full-cast feel.",
    },
    {
      icon: <Camera className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />,
      title: "Self-Tape Recording",
      description: "Record unlimited video or audio takes of your performance directly within the app.",
    },
    {
      icon: <BrainCircuit className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />,
      title: "Line Memorization Helper",
      description: "Utilize specialized modes to master your lines, with options to hide or gradually reveal them.",
    },
    {
      icon: <ScrollText className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />,
      title: "Smart Auto-Scroll",
      description: "Focus on your acting, not the screen. The script intelligently scrolls along with the dialogue.",
    },
  ]

  const stats = [
    { icon: <Users className="h-5 w-5" />, value: "10K+", label: "Active Users" },
    { icon: <Star className="h-5 w-5" />, value: "4.3", label: "Rating" },
    { icon: <Clock className="h-5 w-5" />, value: "24/7", label: "Available" },
  ]

  return (
    <div
      className="relative min-h-screen bg-gradient-to-br from-slate-50/80 via-emerald-50/40 to-slate-100/80 dark:from-slate-950/90 dark:via-slate-900/80 dark:to-slate-950/90 text-slate-900 dark:text-slate-50 transition-colors duration-500"
      style={{ fontFamily: "system-ui, -apple-system, sans-serif" }}
    >
      <AnimatedWavyBackground />

      {isLoading && <LoadingOverlay message="Please wait..." />}

      {/* Header with semi-transparent background */}
      <header className="z-20 backdrop-blur-xl bg-white/60 dark:bg-slate-900/60 border-b border-slate-200/30 dark:border-slate-700/30 sticky top-0">
        <nav className="container mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-sm">C</span>
              </div>
              <h1
                className="text-2xl font-bold text-slate-800 dark:text-slate-100 drop-shadow-sm"
                style={{ fontFamily: '"Courier New", Courier, monospace' }}
              >
                Castmate
              </h1>
            </Link>

            <div className="relative flex items-center space-x-4">
              <CompactThemeToggle />
              {status === "authenticated" && session?.user && !isSigningOut ? (
                <>
                  <div
                    className="flex items-center cursor-pointer group"
                    onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  >
                    <div className="relative">
                      <Image
                        src={session.user.image || "/default-avatar.png"}
                        alt={`${session.user.name}'s profile`}
                        width={40}
                        height={40}
                        className="rounded-full border-2 border-emerald-200 dark:border-emerald-800 group-hover:border-emerald-400 dark:group-hover:border-emerald-600 transition-colors shadow-lg"
                      />
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full border-2 border-white dark:border-slate-900" />
                    </div>
                  </div>
                  {isUserMenuOpen && (
                    <div className="absolute top-16 right-0 bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl p-6 rounded-2xl shadow-2xl w-72 z-50 border border-slate-200/50 dark:border-slate-700/50">
                      <button
                        className="absolute top-3 right-3 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                      <div className="flex flex-col items-center">
                        <Image
                          src={session.user.image || "/default-avatar.png"}
                          alt="User Profile"
                          width={48}
                          height={48}
                          className="rounded-full mb-3 border-2 border-emerald-200 dark:border-emerald-800 shadow-lg"
                        />
                        <h2 className="text-lg font-semibold text-center">{session.user.name}</h2>
                        <p className="text-sm text-slate-600 dark:text-slate-400 text-center mb-6">
                          {session.user.email}
                        </p>

                        <div className="w-full space-y-3">
                          {isProfileIncomplete && (
                            <button
                              onClick={() => setShowUserDetailsModal(true)}
                              className="w-full bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-[1.02] shadow-lg"
                            >
                              Complete Profile
                            </button>
                          )}
                          <Link href="/questionnaire" className="block w-full">
                            <button className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-[1.02] shadow-lg">
                              Share Feedback
                            </button>
                          </Link>
                          <button
                            onClick={handleSignOut}
                            className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-[1.02] shadow-lg"
                          >
                            Sign Out
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                showSignInButton && (
                  <button
                    onClick={handleSignIn}
                    className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white font-medium py-2.5 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-emerald-500/25"
                  >
                    Sign In with Google
                  </button>
                )
              )}
            </div>
          </div>
        </nav>
      </header>

      {/* Main Content with semi-transparent background */}
      <main className="relative z-10">
        <div className="container mx-auto px-6 py-20 flex flex-col items-center justify-center min-h-[calc(100vh-80px)]">
          <motion.div
            className="flex flex-col items-center text-center"
            variants={containerVariants}
            initial="hidden"
            animate={hasLoaded ? "visible" : "hidden"}
          >
            {/* Hero Section with glass morphism effect */}
            <motion.div
              className="max-w-5xl mx-auto mb-16 p-8 rounded-3xl bg-white/20 dark:bg-slate-900/20 backdrop-blur-sm border border-white/30 dark:border-slate-700/30 shadow-xl"
              variants={itemVariants}
            >
              <div className="flex items-center justify-center space-x-4 md:space-x-8 lg:space-x-10 mb-6">
                <Image
                  src="/Clogo6A.png"
                  alt="Left Face Logo"
                  width={240}
                  height={240}
                  className="object-contain opacity-90 w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 drop-shadow-lg"
                />
                <div className="text-center">
                  <h1
                    className="text-6xl sm:text-7xl md:text-8xl font-semibold text-slate-800 dark:text-slate-100 drop-shadow-lg"
                    style={{ fontFamily: '"Courier New", Courier, monospace' }}
                  >
                    Castmate
                  </h1>
                  <p
                    className="mt-2 text-lg text-slate-600 dark:text-slate-400 drop-shadow-sm"
                    style={{ fontFamily: '"Courier New", Courier, monospace' }}
                  >
                    Never rehearse alone again
                  </p>
                </div>
                <Image
                  src="/Clogo7A.png"
                  alt="Right Face Logo"
                  width={240}
                  height={240}
                  className="object-contain opacity-90 w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 drop-shadow-lg"
                />
              </div>

              {/* Stats with enhanced visibility */}
              <motion.div
                className="flex justify-center items-center space-x-6 sm:space-x-8 my-10 p-4 rounded-2xl bg-white/30 dark:bg-slate-800/30 backdrop-blur-sm"
                variants={itemVariants}
              >
                {stats.map((stat, index) => (
                  <div key={index} className="flex items-center space-x-2 text-slate-600 dark:text-slate-300">
                    <div className="text-slate-700 dark:text-slate-200 drop-shadow-sm">{stat.icon}</div>
                    <span className="font-semibold text-slate-800 dark:text-slate-100 drop-shadow-sm">
                      {stat.value}
                    </span>
                    <span className="text-sm drop-shadow-sm">{stat.label}</span>
                  </div>
                ))}
              </motion.div>

              {/* CTA Buttons with enhanced styling */}
              <motion.div
                variants={itemVariants}
                className="flex flex-col sm:flex-row gap-4 justify-center items-center"
              >
                <motion.button
                  onClick={handleOpenModal}
                  className="w-56 py-3 px-6 rounded-lg bg-[#28a87e] hover:bg-[#23946f] text-white font-semibold text-base shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Start Rehearsing
                </motion.button>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.98 }}>
                  <Link href="/questionnaire">
                    <button className="w-56 py-3 px-6 rounded-lg bg-white/80 dark:bg-slate-200/80 border border-slate-300/50 dark:border-slate-700/50 hover:border-slate-400 dark:hover:border-slate-500 text-slate-800 dark:text-slate-900 font-semibold text-base shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-sm">
                      Share Feedback
                    </button>
                  </Link>
                </motion.div>
              </motion.div>
            </motion.div>

            {/* Features Section */}
            <motion.section className="w-full max-w-7xl mx-auto px-6 py-16" variants={itemVariants}>
              <motion.div className="text-center mb-12" variants={itemVariants}>
                <h2
                  className="text-4xl font-bold text-slate-800 dark:text-slate-100 mb-4 drop-shadow-sm"
                  style={{ fontFamily: '"Courier New", Courier, monospace' }}
                >
                  Everything You Need to Perfect Your Performance
                </h2>
                <p className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto drop-shadow-sm">
                  From script upload to final take, Castmate provides all the tools you need for professional rehearsal
                  and recording.
                </p>
              </motion.div>

              <motion.div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" variants={containerVariants}>
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    className="p-6 rounded-2xl bg-white/30 dark:bg-slate-800/30 backdrop-blur-sm border border-white/40 dark:border-slate-700/40 shadow-xl hover:shadow-2xl transition-all duration-300 hover:bg-white/40 dark:hover:bg-slate-800/40 group"
                    variants={itemVariants}
                    whileHover={{ scale: 1.02, y: -5 }}
                  >
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="p-2 rounded-lg bg-emerald-100/80 dark:bg-emerald-900/40 group-hover:bg-emerald-200/80 dark:group-hover:bg-emerald-800/60 transition-colors">
                        {feature.icon}
                      </div>
                      <h3
                        className="text-lg font-semibold text-slate-800 dark:text-slate-100 drop-shadow-sm"
                        style={{ fontFamily: '"Courier New", Courier, monospace' }}
                      >
                        {feature.title}
                      </h3>
                    </div>
                    <p className="text-slate-600 dark:text-slate-300 leading-relaxed drop-shadow-sm">
                      {feature.description}
                    </p>
                  </motion.div>
                ))}
              </motion.div>
            </motion.section>
          </motion.div>
        </div>
      </main>

      <Readermodal isOpen={isModalOpen} onClose={handleCloseModal} />
      {showUserDetailsModal && session?.user && (
        <UserDetailsModal
          email={session.user.email!}
          name={session.user.name}
          onClose={handleUserDetailsClose}
          onComplete={handleUserDetailsComplete}
        />
      )}
    </div>
  )
}
