import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import getOrCreateUserAgent from '../lib/userAgentManager';

interface UseUserAgentReturn {
  agentId: string | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook to manage user-specific ElevenLabs agent
 * Automatically loads the user's agent ID when they sign in
 */
export function useUserAgent(): UseUserAgentReturn {
  const [agentId, setAgentId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { data: session, status } = useSession();

  const loadUserAgent = async () => {
    if (!session?.user?.email) {
      setAgentId(null);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {


      const userAgentId = await getOrCreateUserAgent(session.user.email);

      if (userAgentId) {

        setAgentId(userAgentId);
        setError(null);
      } else {
        console.error(`[useUserAgent] Failed to get agent for ${session.user.email}`);
        setAgentId(null);
        setError('Failed to load or create user agent');
      }
    } catch (err) {
      console.error(`[useUserAgent] Error loading agent:`, err);
      setAgentId(null);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  // Load agent when user signs in
  useEffect(() => {
    if (status === 'authenticated') {
      loadUserAgent();
    } else if (status === 'unauthenticated') {
      setAgentId(null);
      setError(null);
      setIsLoading(false);
    }
  }, [session?.user?.email, status]);

  return {
    agentId,
    isLoading,
    error,
    refetch: loadUserAgent,
  };
}
