// components/tools/VisualizationAnalyzerTool.ts

interface DataCharacteristics {
  hasTimeColumn: boolean;
  hasCategoricalColumns: boolean;
  hasNumericalColumns: boolean;
  rowCount: number;
  columnCount: number;
  uniqueValuesCount: Record<string, number>;
}

interface QueryIndicators {
  hasTimeIndicators: boolean;
  hasCategoryIndicators: boolean;
  hasProportionIndicators: boolean;
  hasCorrelationIndicators: boolean;
  hasTableIndicators: boolean;
  hasAggregationIndicators: boolean;
  hasTrendIndicators: boolean;
}

interface AnalyzerOptions {
  preferredType?: 'line' | 'bar' | 'pie' | 'scatter' | 'table';
  maxCategories?: number;
  minDataPoints?: number;
}

interface AnalyzerOutput {
  isVisualizationRequest: boolean;
  confidence: number;
  data?: Record<string, unknown>[];
  metadata?: {
    type: 'time-series' | 'categorical' | 'distribution';
    metrics: string[];
    timeUnit?: 'day' | 'month' | 'quarter' | 'year';
    hasCurrency?: boolean;
    hasPercentages?: boolean;
    hasMultipleSeries?: boolean;
  };
  suggestion?: {
    type: 'line' | 'bar' | 'pie' | 'scatter' | 'table';
    reason: string;
  };
}

export class VisualizationAnalyzerTool {
  static description = `
    VisualizationAnalyzerTool determines the most appropriate visualization type based on:
    - Data characteristics (time series, categorical, numerical, etc.)
    - User intent (comparison, trend analysis, distribution, etc.)
    - Data complexity and size
    
    Selection Criteria:
    1. Line Charts: Time series data, continuous trends
    2. Bar Charts: Categorical comparisons, rankings
    3. Pie Charts: Part-to-whole relationships, proportions
    4. Scatter Plots: Variable correlations, distributions
    5. Tables: Raw data, multiple attributes, precise values
  `;

  private readonly timeKeywords = [
    'trend', 'over time', 'monthly', 'yearly', 'daily', 'weekly',
    'timeline', 'period', 'historical', 'forecast', 'progression',
    'evolution', 'change', 'growth', 'decline', 'rate', 'quarter',
    'quarterly'
  ];

  private readonly categoryKeywords = [
    'compare', 'comparison', 'different', 'categories', 'versus',
    'types', 'groups', 'ranking', 'vs', 'breakdown', 'by', 'per',
    'each', 'across', 'between'
  ];

  private readonly proportionKeywords = [
    'percentage', 'proportion', 'share', 'distribution', 'ratio',
    'composition', 'makeup', 'split', 'division', 'allocation',
    'partition', 'segment', 'part', 'portion'
  ];

  private readonly correlationKeywords = [
    'correlation', 'relationship', 'between', 'against', 'scatter',
    'distribution', 'variables', 'dependency', 'connection',
    'association', 'linked', 'related'
  ];

  private readonly tableKeywords = [
    'raw data', 'details', 'specific values', 'exact numbers',
    'list', 'table', 'spreadsheet', 'rows', 'columns', 'entries',
    'records', 'items', 'values', 'data points'
  ];

  private readonly aggregationKeywords = [
    'sum', 'total', 'average', 'mean', 'median', 'aggregate',
    'count', 'maximum', 'minimum', 'summary', 'statistics'
  ];

  private readonly currencyRegex = /\$|USD|EUR|GBP|¥|€|£/;
  private readonly percentageRegex = /%|percent|percentage/i;

  async call(params: { 
    query: string; 
    data: any[]; 
    options?: AnalyzerOptions;
  }): Promise<AnalyzerOutput> {
    try {
      console.log("VisualizationAnalyzerTool: Analyzing request", { query: params.query });

      const queryIndicators = this.analyzeQueryIntent(params.query);
      const dataCharacteristics = this.analyzeDataCharacteristics(params.data);
      
      const metadata = {
        type: this.determineDataType(queryIndicators, dataCharacteristics),
        metrics: this.identifyMetrics(params.data),
        timeUnit: this.determineTimeUnit(params.data),
        hasCurrency: this.currencyRegex.test(params.query),
        hasPercentages: this.percentageRegex.test(params.query),
        hasMultipleSeries: this.hasMultipleMetrics(params.data)
      };

      return this.determineVisualizationType(
        queryIndicators,
        dataCharacteristics,
        metadata,
        params.data,
        params.options
      );
    } catch (error) {
      console.error("VisualizationAnalyzerTool error:", error);
      return {
        isVisualizationRequest: false,
        confidence: 0
      };
    }
  }

  private analyzeQueryIntent(query: string): QueryIndicators {
    const normalizedQuery = query.toLowerCase();

    return {
      hasTimeIndicators: this.checkKeywordPresence(normalizedQuery, this.timeKeywords),
      hasCategoryIndicators: this.checkKeywordPresence(normalizedQuery, this.categoryKeywords),
      hasProportionIndicators: this.checkKeywordPresence(normalizedQuery, this.proportionKeywords),
      hasCorrelationIndicators: this.checkKeywordPresence(normalizedQuery, this.correlationKeywords),
      hasTableIndicators: this.checkKeywordPresence(normalizedQuery, this.tableKeywords),
      hasAggregationIndicators: this.checkKeywordPresence(normalizedQuery, this.aggregationKeywords),
      hasTrendIndicators: this.checkTrendIndicators(normalizedQuery)
    };
  }

  private checkKeywordPresence(query: string, keywords: string[]): boolean {
    return keywords.some(keyword => query.includes(keyword));
  }

  private checkTrendIndicators(query: string): boolean {
    const trendPatterns = [
      /\b(show|display|view|analyze)\s+trends?\b/i,
      /\b(over|across|through|throughout)\s+time\b/i,
      /\b(increase|decrease|change)\s+in\b/i,
      /\b(historical|future|forecast)\s+pattern\b/i
    ];
    return trendPatterns.some(pattern => pattern.test(query));
  }

  private analyzeDataCharacteristics(data: any[]): DataCharacteristics {
    if (!data.length) {
      throw new Error("Empty dataset provided");
    }

    const columns = Object.keys(data[0]);
    const uniqueValuesCount: Record<string, number> = {};
    let hasTimeColumn = false;
    let hasCategoricalColumns = false;
    let hasNumericalColumns = false;

    columns.forEach(column => {
      const values = data.map(row => row[column]);
      uniqueValuesCount[column] = new Set(values).size;
      
      const columnType = this.inferColumnType(values);
      if (columnType === 'time') hasTimeColumn = true;
      if (columnType === 'categorical') hasCategoricalColumns = true;
      if (columnType === 'numerical') hasNumericalColumns = true;
    });

    return {
      hasTimeColumn,
      hasCategoricalColumns,
      hasNumericalColumns,
      rowCount: data.length,
      columnCount: columns.length,
      uniqueValuesCount
    };
  }

  private inferColumnType(values: unknown[]): 'time' | 'numerical' | 'categorical' | 'text' {
    if (!values.length) return 'text';

    // Check for dates
    if (values.every(value => !isNaN(Date.parse(String(value))))) {
      return 'time';
    }

    // Check for numbers
    if (values.every(value => 
      typeof value === 'number' || 
      (typeof value === 'string' && !isNaN(parseFloat(value)))
    )) {
      return 'numerical';
    }

    // Check for categorical data
    const uniqueValues = new Set(values);
    if (uniqueValues.size <= Math.sqrt(values.length)) {
      return 'categorical';
    }

    return 'text';
  }

  private determineTimeUnit(data: any[]): 'day' | 'month' | 'quarter' | 'year' | undefined {
    if (!data.length) return undefined;

    const timeColumns = Object.keys(data[0]).filter(key => 
      this.inferColumnType([data[0][key]]) === 'time'
    );

    if (!timeColumns.length) return undefined;

    const sampleValue = String(data[0][timeColumns[0]]).toLowerCase();
    if (sampleValue.includes('q')) return 'quarter';
    if (/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i.test(sampleValue)) return 'month';
    if (/^\d{4}$/.test(sampleValue)) return 'year';
    if (/^\d{4}-\d{2}-\d{2}$/.test(sampleValue)) return 'day';

    return undefined;
  }

  private identifyMetrics(data: any[]): string[] {
    if (!data.length) return [];

    return Object.entries(data[0])
      .filter(([_, value]) => typeof value === 'number' || !isNaN(Number(value)))
      .map(([key]) => key);
  }

  private hasMultipleMetrics(data: any[]): boolean {
    return this.identifyMetrics(data).length > 1;
  }

  private determineDataType(
    indicators: QueryIndicators,
    characteristics: DataCharacteristics
  ): 'time-series' | 'categorical' | 'distribution' {
    if (indicators.hasTimeIndicators && characteristics.hasTimeColumn) {
      return 'time-series';
    }
    if (indicators.hasProportionIndicators || characteristics.hasCategoricalColumns) {
      return 'categorical';
    }
    return 'distribution';
  }

  private shouldUseTable(
    indicators: QueryIndicators, 
    characteristics: DataCharacteristics
  ): boolean {
    return (
      indicators.hasTableIndicators ||
      characteristics.columnCount > 5 ||
      characteristics.rowCount > 1000 ||
      (characteristics.hasTimeColumn && characteristics.hasCategoricalColumns && characteristics.hasNumericalColumns)
    );
  }

  private shouldUseLineChart(
    indicators: QueryIndicators,
    characteristics: DataCharacteristics
  ): boolean {
    return (
      (indicators.hasTimeIndicators || indicators.hasTrendIndicators) &&
      characteristics.hasTimeColumn &&
      characteristics.hasNumericalColumns
    );
  }

  private shouldUseBarChart(
    indicators: QueryIndicators,
    characteristics: DataCharacteristics
  ): boolean {
    return (
      (indicators.hasCategoryIndicators || indicators.hasAggregationIndicators) &&
      characteristics.hasCategoricalColumns &&
      characteristics.hasNumericalColumns &&
      Object.values(characteristics.uniqueValuesCount).some(count => count <= 20)
    );
  }

  private shouldUsePieChart(
    indicators: QueryIndicators,
    characteristics: DataCharacteristics
  ): boolean {
    return (
      indicators.hasProportionIndicators &&
      characteristics.hasCategoricalColumns &&
      characteristics.hasNumericalColumns &&
      Object.values(characteristics.uniqueValuesCount).some(count => count <= 10)
    );
  }

  private shouldUseScatterPlot(
    indicators: QueryIndicators,
    characteristics: DataCharacteristics
  ): boolean {
    return (
      indicators.hasCorrelationIndicators &&
      characteristics.hasNumericalColumns &&
      Object.values(characteristics.uniqueValuesCount).filter(count => count > 20).length >= 2
    );
  }

  private determineVisualizationType(
    indicators: QueryIndicators,
    characteristics: DataCharacteristics,
    metadata: NonNullable<AnalyzerOutput['metadata']>,
    data: any[],
    options?: AnalyzerOptions
  ): AnalyzerOutput {
    // Honor preferred type if specified
    if (options?.preferredType) {
      return {
        isVisualizationRequest: true,
        confidence: 1,
        data,
        metadata,
        suggestion: {
          type: options.preferredType,
          reason: `User preferred visualization type: ${options.preferredType}`
        }
      };
    }

    // Check each visualization type
    if (this.shouldUseLineChart(indicators, characteristics)) {
      return {
        isVisualizationRequest: true,
        confidence: 0.9,
        data,
        metadata,
        suggestion: {
          type: 'line',
          reason: 'Time-based data showing trends over time'
        }
      };
    }

    if (this.shouldUseBarChart(indicators, characteristics)) {
      return {
        isVisualizationRequest: true,
        confidence: 0.9,
        data,
        metadata,
        suggestion: {
          type: 'bar',
          reason: 'Categorical comparison with numerical values'
        }
      };
    }

    if (this.shouldUsePieChart(indicators, characteristics)) {
      return {
        isVisualizationRequest: true,
        confidence: 0.8,
        data,
        metadata,
        suggestion: {
          type: 'pie',
          reason: 'Proportional data showing part-to-whole relationships'
        }
      };
    }

    if (this.shouldUseScatterPlot(indicators, characteristics)) {
      return {
        isVisualizationRequest: true,
        confidence: 0.8,
        data,
        metadata,
        suggestion: {
          type: 'scatter',
          reason: 'Correlation analysis between numerical variables'
        }
      };
    }

    // Default to table
    return {
      isVisualizationRequest: true,
      confidence: 0.7,
      data,
      metadata,
      suggestion: {
        type: 'table',
        reason: 'Complex data structure better suited for tabular display'
      }
    };
  }
}