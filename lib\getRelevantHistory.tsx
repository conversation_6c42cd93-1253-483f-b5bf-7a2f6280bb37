import { OpenAIEmbeddings } from "@langchain/openai";

// Helper function to convert text to embeddings
async function getEmbedding(text: string) {
  const embeddings = new OpenAIEmbeddings();
  return await embeddings.embedQuery(text);
}

// Helper function to calculate cosine similarity between two vectors
export async function calculateCosineSimilarity(vecA: number[], vecB: number[]) {
  const dotProduct = vecA.reduce((acc, cur, i) => acc + cur * vecB[i], 0);
  const magnitudeA = Math.sqrt(vecA.reduce((acc, cur) => acc + cur * cur, 0));
  const magnitudeB = Math.sqrt(vecB.reduce((acc, cur) => acc + cur * cur, 0));
  return dotProduct / (magnitudeA * magnitudeB);
}

// Function to get relevant history
export async function getRelevantHistory(
  prompt: string,
  context: string
): Promise<string> {
  // Generate embedding for the user prompt
  const promptEmbedding = await getEmbedding(prompt);

  // Generate embedding for the context
  const contextEmbedding = await getEmbedding(context);

  // Calculate similarity between the prompt and the context
  const similarity = await calculateCosineSimilarity(promptEmbedding, contextEmbedding);

  // Define a similarity threshold and check if the context is relevant
  if (similarity >= 0.7) {
    // Return the relevant context if similarity is above the threshold
    return context;
  }

  //console.log("What we can use fron chat history :", context)
  // Return an empty string if the context is not relevant
  return '';
}
