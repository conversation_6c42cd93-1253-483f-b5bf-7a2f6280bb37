import { NextRequest, NextResponse } from 'next/server';
import { adminDb, adminStorage } from 'components/firebase-admin';
import { v4 as uuidv4 } from 'uuid';

interface ChartJobData {
  prompt: string;
  id: string;
  userId: string;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  let jobData: ChartJobData | undefined;
  try {
    // 1) Parse and validate request
    const { docId, userId, fileName, fileType, fileUrl, isChart, category, chartType, description } = await req.json();
    if (!docId || !userId || !fileName || !fileType || !fileUrl || !category || !chartType || !description) {
      throw new Error('Missing required parameters in request.');
    }
    //console.log(`Processing chart file: ${fileName}`);

    // 2) Fetch the chart data from the provided URL
    let chartData: string;
    try {
      const response = await fetch(fileUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch chart data: ${response.statusText}`);
      }
      chartData = await response.text();
      //console.log(`Chart data fetched for file: ${fileName}`);
    } catch (error) {
      console.error('Chart data fetch error:', error);
      throw new Error(`Failed to fetch chart data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 3) Upload to Firebase Storage using user-specific path
    const bucket = adminStorage.bucket();
    const filePath = `users/${userId}/charts/${fileName}`;
    const file = bucket.file(filePath);

    try {
      await file.save(chartData, {
        metadata: {
          contentType: fileType,
          metadata: {
            docId,
            userId,
            generatedAt: new Date().toISOString()
          }
        }
      });
      //console.log(`Chart data uploaded to Firebase Storage for file: ${fileName}`);
    } catch (error) {
      console.error('Firebase Storage upload error:', error);
      throw new Error(`Failed to upload chart data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 4) Generate signed URL
    let downloadUrl;
    try {
      [downloadUrl] = await file.getSignedUrl({
        action: 'read',
        expires: '03-01-2500'
      });
      //console.log(`Signed URL generated for file: ${fileName}`);
    } catch (error) {
      console.error('Signed URL generation error:', error);
      throw new Error(`Failed to generate signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 5) Create record in files collection with namespace
    const namespace = uuidv4();
    try {
      const fileRef = adminDb.collection('users').doc(userId).collection('files').doc(namespace);
      await fileRef.set({
        category,
        createdAt: new Date(),
        downloadUrl,
        isChart,
        name: fileName,
        namespace,
        ref: `uploads/${userId}/charts/${fileName}`,
        size: chartData.length,
        type: fileType,
        chartType,
        description,
        docId
      });
      //console.log(`Files collection record created for file: ${fileName} with namespace: ${namespace}`);
    } catch (error) {
      console.error('Files collection update error:', error);
      throw new Error(`Failed to create files record: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 6) Update the original chart document with the file information
    try {
      const chartRef = adminDb.collection('users').doc(userId).collection('charts').doc(docId);
      await chartRef.update({
        fileUrl: downloadUrl,
        fileName,
        fileType,
        namespace,
        updatedAt: new Date()
      });
      //console.log(`Chart document updated for docId: ${docId}`);
    } catch (error) {
      console.error('Chart document update error:', error);
      throw new Error(`Failed to update chart document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 7) Return success response with namespace
    return NextResponse.json({
      success: true,
      fileUrl: downloadUrl,
      namespace,
      docId
    });

  } catch (error) {
    console.error('Chart file processing error:', error);

    // Return error response
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to process chart file',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

