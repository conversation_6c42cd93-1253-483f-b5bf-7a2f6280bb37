import { adminDb, adminStorage } from 'components/firebase-admin';
import { pineconeClient } from '../../lib/pinecone-client';

export const deleteDocumentAndChatsByNamespace = async (userId: string, namespace: string, scriptName?: string) => {
  try {
    // 1. Delete the document metadata from Firestore (user's files collection)
    const filesRef = adminDb.collection('users').doc(userId).collection('files');
    const filesQuery = filesRef.where('namespace', '==', namespace);
    const querySnapshot = await filesQuery.get();

    if (querySnapshot.empty) {
      throw new Error(`No document found with namespace: ${namespace}`);
    }

    let fileDocumentId = '';

    // Loop through each file document and delete it
    for (const fileDoc of querySnapshot.docs) {
      fileDocumentId = fileDoc.id;
      await fileDoc.ref.delete();
      await deleteAssociatedChats(userId, fileDocumentId);
    }

    // 2. Delete the embeddings from Pinecone
    await deleteVectorsFromPinecone(namespace);

    // 3. Delete the document chunks from Firestore (byteStoreCollection)
    await deleteDocumentChunks(namespace,userId);

    // 4. Delete the file from Firebase Storage
    await deleteFileFromStorage(namespace, userId);

    // 5. Delete formatted script data from Firestore
    await deleteFormattedScript(namespace, userId);

    // 6. Delete ElevenLabs data and remove from knowledge base
    await deleteElevenLabsData(namespace, userId, scriptName);

    // 7. Add a notification after the document and associated chats have been deleted
    await addNotification(userId, `Document with namespace: ${namespace} has been successfully removed.`);

    return `Document and associated chats with namespace: ${namespace} have been successfully deleted.`;
  } catch (error) {
    console.error('Error deleting document and chats by namespace:', error);
    throw new Error('Error deleting document and chats.');
  }
};

const deleteVectorsFromPinecone = async (namespace: string) => {
  try {
    const indexName = process.env.PINECONE_INDEX || 'castmate';
    if (!indexName) {
      throw new Error('PINECONE_INDEX environment variable is not set.');
    }

    const index = pineconeClient.Index(indexName);
    await index.namespace(namespace).deleteAll();

    //console.log(`Vectors in namespace '${namespace}' have been deleted from Pinecone.`);
  } catch (error) {
    console.error('Error deleting vectors from Pinecone:', error);
    throw new Error('Error deleting vectors from Pinecone.');
  }
};

const deleteAssociatedChats = async (userId: string, fileDocumentId: string) => {
  try {
    const chatsRef = adminDb.collection('users').doc(userId).collection('chats');
    const chatsQuery = chatsRef.where('fileDocumentId', '==', fileDocumentId);
    const chatSnapshot = await chatsQuery.get();

    if (!chatSnapshot.empty) {
      for (const chatDoc of chatSnapshot.docs) {
        await deleteAssociatedMessages(userId, chatDoc.id);
        await chatDoc.ref.delete();
      }
    }
  } catch (error) {
    console.error("Error deleting associated chats:", error);
    throw new Error("Error deleting associated chats.");
  }
};

const deleteAssociatedMessages = async (userId: string, chatId: string) => {
  try {
    const messagesRef = adminDb.collection('users').doc(userId).collection('chats').doc(chatId).collection('messages');
    const messagesSnapshot = await messagesRef.get();
    for (const messageDoc of messagesSnapshot.docs) {
      await messageDoc.ref.delete();
    }
  } catch (error) {
    console.error("Error deleting associated messages:", error);
    throw new Error("Error deleting associated messages.");
  }
};

const deleteDocumentChunks = async (namespace: string, userId: string ) => {
  try {

    const byteCollection = `users/${userId}/byteStoreCollection`;

    const byteStoreCollectionRef = adminDb.collection(byteCollection);
    const chunksQuerySnapshot = await byteStoreCollectionRef.where('metadata.doc_id', '==', namespace).get();

    if (!chunksQuerySnapshot.empty) {
      const batch = adminDb.batch();
      chunksQuerySnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });
      await batch.commit();
      //console.log(`Document chunks for namespace ${namespace} deleted from Firestore.`);
    } else {
      console.warn(`No document chunks found for namespace ${namespace} in Firestore.`);
    }
  } catch (error) {
    console.error('Error deleting document chunks from Firestore:', error);
    throw new Error('Error deleting document chunks from Firestore.');
  }
};

const deleteFileFromStorage = async (namespace: string, userId: string) => {
  try {
    const bucketName = process.env.FIREBASE_STORAGE_BUCKET;
    if (!bucketName) {
      throw new Error('FIREBASE_STORAGE_BUCKET environment variable is not set.');
    }

    const bucket = adminStorage.bucket(bucketName);
    const filePath = `uploads/${userId}/${namespace}`;  // Include user email in the file path

    await bucket.file(filePath).delete();
    //console.log(`File ${namespace} deleted from Firebase Storage.`);
  } catch (error) {
    console.error('Error deleting file from Firebase Storage:', error);
    throw new Error('Error deleting file from Firebase Storage.');
  }
};


const addNotification = async (userId: string, message: string) => {
  try {
    const notificationsRef = adminDb.collection('users').doc(userId).collection('notifications');
    await notificationsRef.add({
      message: message,
      timestamp: new Date(),
      read: false
    });
    //console.log(`Notification added for user: ${userId} - ${message}`);
  } catch (error) {
    console.error('Error adding notification:', error);
    throw new Error('Error adding notification.');
  }
};

const deleteFormattedScript = async (namespace: string, userId: string) => {
  try {
    const formattedScriptsRef = adminDb.collection('users').doc(userId).collection('formatted_scripts');
    const formattedScriptDoc = formattedScriptsRef.doc(namespace);

    const docSnapshot = await formattedScriptDoc.get();
    if (docSnapshot.exists) {
      await formattedScriptDoc.delete();
      //console.log(`Formatted script for namespace ${namespace} deleted from Firestore.`);
    } else {
      console.warn(`No formatted script found for namespace ${namespace} in Firestore.`);
    }
  } catch (error) {
    console.error('Error deleting formatted script from Firestore:', error);
    throw new Error('Error deleting formatted script from Firestore.');
  }
};

const deleteElevenLabsData = async (namespace: string, userId: string, scriptName?: string) => {
  try {
    // 1. Determine the file name to search for in ElevenLabs Knowledge Base
    let fileName: string | null = null;

    // Priority 1: Use script name from sidebar if provided
    if (scriptName) {
      fileName = scriptName;
      //console.log(`Using script name from sidebar: ${fileName} for namespace: ${namespace}`);
    } else {
      // Priority 2: Get file name from the files collection
      const filesRef = adminDb.collection('users').doc(userId).collection('files');
      const filesQuery = filesRef.where('namespace', '==', namespace);
      const filesSnapshot = await filesQuery.get();

      if (!filesSnapshot.empty) {
        const fileData = filesSnapshot.docs[0].data();
        fileName = fileData.name;
        //console.log(`Found file name from files collection: ${fileName} for namespace: ${namespace}`);
      } else {
        console.warn(`No file found for namespace ${namespace} in files collection.`);
      }
    }

    // 2. Get ElevenLabs document ID from Firestore using the namespace as document ID
    const elevenLabsDataRef = adminDb.collection('users').doc(userId).collection('ElevenLabsData');
    const elevenLabsDoc = elevenLabsDataRef.doc(namespace);
    const elevenLabsSnapshot = await elevenLabsDoc.get();

    let elevenLabsDocumentId: string | null = null;

    // Delete ElevenLabs metadata from Firestore and get document ID
    if (elevenLabsSnapshot.exists) {
      const data = elevenLabsSnapshot.data();
      elevenLabsDocumentId = data?.knowledgeBaseDocId || data?.document_id || data?.documentId;

      // Priority 3: Use stored original filename if no other filename found
      if (!fileName && data?.originalFileName) {
        fileName = data.originalFileName;
        //console.log(`Using stored original filename: ${fileName} for namespace: ${namespace}`);
      }

      await elevenLabsDoc.delete();
      //console.log(`ElevenLabs data for namespace ${namespace} deleted from Firestore.`);
    } else {
      console.warn(`No ElevenLabs data found for namespace ${namespace} in Firestore.`);
    }

    // 3. Delete from ElevenLabs Knowledge Base
    if (elevenLabsDocumentId) {
      // Try to delete using the stored document ID first
      try {
        const response = await fetch(`https://api.elevenlabs.io/v1/convai/knowledge-base/${elevenLabsDocumentId}`, {
          method: 'DELETE',
          headers: {
            'xi-api-key': process.env.ELEVENLABS_API_KEY || '***************************************************',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            force: true // Force delete even if used by agents
          })
        });

        if (response.ok) {
          //console.log(`Document ${elevenLabsDocumentId} deleted from ElevenLabs Knowledge Base.`);
        } else {
          const errorData = await response.json();
          console.error(`Failed to delete document ${elevenLabsDocumentId} from ElevenLabs:`, errorData);
          // Don't throw error here as the local data is already cleaned up
        }
      } catch (error) {
        console.error(`Error deleting document ${elevenLabsDocumentId} from ElevenLabs Knowledge Base:`, error);
        // Don't throw error here as the local data is already cleaned up
      }
    } else if (fileName) {
      // If no document ID stored, try to find and delete by file name
      try {
        //console.log(`Attempting to find and delete ElevenLabs document by file name: ${fileName}`);

        // Get list of knowledge base documents
        const listResponse = await fetch('https://api.elevenlabs.io/v1/convai/knowledge-base', {
          method: 'GET',
          headers: {
            'xi-api-key': process.env.ELEVENLABS_API_KEY || '***************************************************',
            'Content-Type': 'application/json'
          }
        });

        if (listResponse.ok) {
          const documents = await listResponse.json();
          //console.log(`Found ${documents.length} documents in ElevenLabs Knowledge Base`);

          // Find document by name
          const targetDoc = documents.find((doc: any) => doc.name === fileName);

          if (targetDoc) {
            //console.log(`Found matching document: ${targetDoc.document_id} with name: ${targetDoc.name}`);

            // Delete the found document
            const deleteResponse = await fetch(`https://api.elevenlabs.io/v1/convai/knowledge-base/${targetDoc.document_id}`, {
              method: 'DELETE',
              headers: {
                'xi-api-key': process.env.ELEVENLABS_API_KEY || '***************************************************',
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                force: true // Force delete even if used by agents
              })
            });

            if (deleteResponse.ok) {
              //console.log(`Document ${targetDoc.document_id} (${fileName}) deleted from ElevenLabs Knowledge Base.`);
            } else {
              const errorData = await deleteResponse.json();
              console.error(`Failed to delete document ${targetDoc.document_id} from ElevenLabs:`, errorData);
            }
          } else {
            console.warn(`No document found in ElevenLabs Knowledge Base with name: ${fileName}`);
          }
        } else {
          console.error('Failed to list ElevenLabs Knowledge Base documents');
        }
      } catch (error) {
        console.error(`Error finding/deleting document by name ${fileName} from ElevenLabs:`, error);
        // Don't throw error here as the local data is already cleaned up
      }
    } else {
      console.warn('No ElevenLabs document ID or file name available for deletion');
    }
  } catch (error) {
    console.error('Error deleting ElevenLabs data:', error);
    throw new Error('Error deleting ElevenLabs data.');
  }
};