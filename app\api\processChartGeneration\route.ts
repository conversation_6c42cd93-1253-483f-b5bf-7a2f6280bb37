import { NextRequest, NextResponse } from 'next/server';
import { adminDb, adminStorage } from 'components/firebase-admin';
import { VisualizationAgent } from 'components/Agents/VisualizationAgent';
import { v4 as uuidv4 } from 'uuid';

export async function POST(req: NextRequest): Promise<NextResponse> {
  let jobData: any;
  try {
    const { jobId, userId } = await req.json();
    if (!jobId || !userId) {
      throw new Error('Missing jobId or userId in request.');
    }
    //console.log(`Processing chart job: ${jobId}`);

    const apiKey = process.env.GROQ_API_KEY;
    if (!apiKey) {
      throw new Error('Groq API key not configured in environment variables.');
    }

    const visualizationAgent = new VisualizationAgent();

    let jobDoc;
    try {
      const jobRef = adminDb.collection('users').doc(userId).collection('charts').doc(jobId);
      jobDoc = await jobRef.get();
      //console.log(`Firestore query completed for job: ${jobId}`);

      if (!jobDoc.exists) {
        throw new Error(`Job ${jobId} not found for user ${userId}`);
      }

      jobData = jobDoc.data();
      if (!jobData?.prompt) {
        throw new Error(`No prompt found in job ${jobId}`);
      }
      //console.log(`Job data retrieved for user: ${userId}`);
    } catch (error) {
      console.error('Firestore query error:', error);
      throw new Error(`Failed to fetch job: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    let visualizationResponse;
    try {
      const processRequest = {
        query: jobData.prompt,
        data: jobData.data || [],
        options: {
          type: 'chart' as const,
          features: [],
          formatting: {}
        }
      };

      visualizationResponse = await visualizationAgent.process(processRequest);

      if (!visualizationResponse.success || !visualizationResponse.visualization) {
        throw new Error(visualizationResponse.error || 'Failed to generate visualization');
      }

      //console.log(`Chart generated for job: ${jobId}`);
    } catch (error) {
      console.error('Chart generation error:', error);
      throw new Error(`Failed to generate chart: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    const chartData = JSON.stringify(visualizationResponse.visualization);

    const bucket = adminStorage.bucket();
    const filePath = `users/${userId}/generated/${jobId}.json`;
    const file = bucket.file(filePath);

    try {
      await file.save(chartData, {
        metadata: {
          contentType: 'application/json',
          metadata: {
            jobId,
            userId,
            generatedAt: new Date().toISOString()
          }
        }
      });
      //console.log(`Chart data uploaded to Firebase Storage for job: ${jobId}`);
    } catch (error) {
      console.error('Firebase Storage upload error:', error);
      throw new Error(`Failed to upload chart data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    let downloadUrl: string;
    try {
      [downloadUrl] = await file.getSignedUrl({
        action: 'read',
        expires: '03-01-2500'
      });
      //console.log(`Signed URL generated for job: ${jobId}`);
    } catch (error) {
      console.error('Signed URL generation error:', error);
      throw new Error(`Failed to generate signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    const jobRef = adminDb.collection('users').doc(userId).collection('charts').doc(jobId);
    try {
      await jobRef.update({
        status: 'completed',
        chartUrl: downloadUrl,
        updatedAt: new Date(),
        processedAt: new Date(),
        chartType: visualizationResponse.visualization.subtype,
        chartConfig: visualizationResponse.visualization.config,
        chartMetadata: visualizationResponse.visualization.metadata,
        confidence: visualizationResponse.visualization.confidence,
        reasoning: visualizationResponse.visualization.reasoning
      });
      //console.log(`Firestore job status updated for job: ${jobId}`);
    } catch (error) {
      console.error('Firestore update error:', error);
      throw new Error(`Failed to update job status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    const namespace = uuidv4();
    try {
      const fileRef = adminDb.collection('users').doc(userId).collection('files').doc(namespace);
      await fileRef.set({
        category: 'Charts',
        createdAt: new Date(),
        downloadUrl: downloadUrl,
        isChart: true,
        name: jobData.prompt,
        namespace: namespace,
        ref: `uploads/${userId}/generated/${jobId}.json`,
        size: chartData.length,
        type: 'application/json',
        jobId: jobId,
        description: jobData.prompt,
        chartType: visualizationResponse.visualization.subtype,
        chartConfig: visualizationResponse.visualization.config,
        chartMetadata: visualizationResponse.visualization.metadata,
        confidence: visualizationResponse.visualization.confidence,
        reasoning: visualizationResponse.visualization.reasoning
      });
      //console.log(`Files collection record created for job: ${jobId} with namespace: ${namespace}`);
    } catch (error) {
      console.error('Files collection update error:', error);
      throw new Error(`Failed to create files record: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return NextResponse.json({
      success: true,
      chartUrl: downloadUrl,
      namespace: namespace,
      jobId,
      chartType: visualizationResponse.visualization.subtype,
      chartConfig: visualizationResponse.visualization.config,
      chartMetadata: visualizationResponse.visualization.metadata,
      confidence: visualizationResponse.visualization.confidence,
      reasoning: visualizationResponse.visualization.reasoning
    });

  } catch (error) {
    console.error('Chart generation process error:', error);

    try {
      if (jobData?.userId && jobData?.id) {
        const jobRef = adminDb.collection('users').doc(jobData.userId).collection('charts').doc(jobData.id);
        await jobRef.update({
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
          updatedAt: new Date(),
          failedAt: new Date()
        });
        //console.log(`Job status updated to failed for job: ${jobData.id}`);
      }
    } catch (updateError) {
      console.error('Failed to update job status:', updateError);
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process chart',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}