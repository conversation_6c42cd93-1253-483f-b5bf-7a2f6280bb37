"use client"

import React, { useEffect, useState } from 'react'
import { doc, getDoc } from 'firebase/firestore'
import { db } from 'components/firebase' // Adjust the path to your Firebase configuration
import DocumentReader from 'components/DocViewer/DocumentReader'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Loader2Icon, Menu } from 'lucide-react' // Import the loader and menu icons
import SideBar from 'components/SideBar'
import Header from "components/Header"

export default function DocViewers({ params }: { params: { id: string } }) {
  const { id } = params
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null)
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const router = useRouter()
  const { data: session } = useSession()

  useEffect(() => {
    const fetchDownloadUrl = async () => {
      if (!session) return

      try {
        const userId = session.user?.email // Use the user's email as the ID
        const docRef = doc(db, 'users', userId!, 'files', id)
        const docSnap = await getDoc(docRef)

        if (docSnap.exists()) {
          const data = docSnap.data()
          const url = data.downloadUrl // Assuming the download URL is stored under this key
          //console.log('Download URL:', url)
          setDownloadUrl(url)
        } else {
          console.error('No such document!')
          router.push('/404') // Redirect to a 404 page or handle as needed
        }
      } catch (error) {
        console.error('Error fetching download URL:', error)
        router.push('/error') // Redirect to an error page or handle as needed
      }
    }

    fetchDownloadUrl()
  }, [id, session, router])

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen)

  return (
    <div className="flex flex-col h-screen">
      <Header />
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar for larger screens */}
        <aside className="hidden md:block w-64 overflow-y-auto border-r bg-gray-100 lg:block">
          <SideBar />
        </aside>

        {/* Mobile sidebar */}
        <div className={`fixed inset-0 z-20 transition-opacity ${isSidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
          <div className="absolute inset-0 bg-gray-500 opacity-75" onClick={toggleSidebar}></div>
          <aside className={`fixed inset-y-0 left-0 w-64 transition duration-300 transform bg-gray-100 ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
            <SideBar />
          </aside>
        </div>

        {/* Main content area */}
        <main className="flex-1 overflow-hidden">
          {/* Mobile menu button */}
          <button 
            className="lg:hidden fixed top-4 left-4 z-30 p-2 rounded-md bg-gray-200 text-gray-700"
            onClick={toggleSidebar}
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">Toggle navigation menu</span>
          </button>

          {downloadUrl ? (
            <div className="h-full overflow-hidden scroll-m-8">
              <DocumentReader url={downloadUrl} />
            </div>
          ) : (
            <div className="flex justify-center items-center h-full">
              <Loader2Icon className="animate-spin w-12 h-12 text-gray-500" />
            </div>
          )}
        </main>
      </div>
    </div>
  )
}