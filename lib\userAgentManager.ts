import { v4 as uuidv4 } from 'uuid';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '../components/firebase';
import { checkAgentExists, createUserAgent, validateAndFixUserAgentConfig } from '../components/scriptreaderAI/elevenlabs';

// In-memory cache to prevent duplicate agent creation for the same user
const agentCreationCache = new Map<string, Promise<AgentCreationResult>>();

// Type definitions
export interface UserAgentData {
  elevenLabsAgentId: string;
  agentCreatedAt: string;
  agentStatus: 'active' | 'failed' | 'creating';
  lastAgentCheck?: string;
  agentCreationAttempts?: number;
}

export interface AgentCreationResult {
  success: boolean;
  agentId?: string;
  error?: string;
  isNewAgent?: boolean;
}

/**
 * Validates if an agent ID follows the correct format: userEmail-uuid
 * @param agentId - Agent ID to validate
 * @returns True if valid format, false otherwise
 */
export function isValidUserAgentId(agentId: string): boolean {
  if (!agentId || typeof agentId !== 'string') {
    return false;
  }

  // Pattern: <EMAIL>-uuid
  const pattern = /^[^@\s]+@[^@\s]+\.[^@\s]+-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i;
  return pattern.test(agentId);
}

/**
 * Generates a unique agent ID using the format: userEmail-uuid
 * @param userEmail - Email of the user
 * @returns Unique agent ID
 */
export function generateUserAgentId(userEmail: string): string {
  if (!userEmail || typeof userEmail !== 'string') {
    throw new Error('User email is required and must be a string');
  }

  const uuid = uuidv4();
  const agentId = `${userEmail}-${uuid}`;

  return agentId;
}

/**
 * Gets the user's ElevenLabs agent ID from their account record
 * @param userEmail - Email of the user
 * @returns Agent ID if exists, null otherwise
 */
export async function getUserAgentId(userEmail: string): Promise<string | null> {
  try {
    if (!userEmail || typeof userEmail !== 'string') {
      throw new Error('User email is required and must be a string');
    }



    const accountDoc = await getDoc(doc(db, 'Accounts', userEmail));

    if (!accountDoc.exists()) {

      return null;
    }

    const userData = accountDoc.data();
    const agentId = userData?.elevenLabsAgentId;

    if (agentId) {

      return agentId;
    } else {

      return null;
    }
  } catch (error) {
    console.error(`[USER_AGENT_MANAGER] Error getting agent ID for ${userEmail}:`, error);
    return null;
  }
}

/**
 * Stores the user's ElevenLabs agent ID in their account record
 * @param userEmail - Email of the user
 * @param agentData - Agent data to store
 */
export async function storeUserAgentData(userEmail: string, agentData: UserAgentData): Promise<void> {
  try {
    if (!userEmail || typeof userEmail !== 'string') {
      throw new Error('User email is required and must be a string');
    }



    const accountRef = doc(db, 'Accounts', userEmail);

    // Update the user's account with agent data
    await updateDoc(accountRef, {
      elevenLabsAgentId: agentData.elevenLabsAgentId,
      agentCreatedAt: agentData.agentCreatedAt,
      agentStatus: agentData.agentStatus,
      lastAgentCheck: agentData.lastAgentCheck || new Date().toISOString(),
      agentCreationAttempts: agentData.agentCreationAttempts || 1,
    });


  } catch (error) {
    console.error(`[USER_AGENT_MANAGER] Error storing agent data for ${userEmail}:`, error);
    throw error;
  }
}

/**
 * Ensures a user has a valid ElevenLabs agent, creating one if necessary
 * FIXED: Added synchronization to prevent duplicate agent creation
 * @param userEmail - Email of the user
 * @param apiKey - Optional ElevenLabs API key
 * @returns Agent creation/verification result
 */
export async function ensureUserAgent(userEmail: string, apiKey?: string): Promise<AgentCreationResult> {
  try {
    if (!userEmail || typeof userEmail !== 'string') {
      return { success: false, error: 'User email is required and must be a string' };
    }

    // FIXED: Check if there's already an ongoing agent creation for this user
    const existingPromise = agentCreationCache.get(userEmail);
    if (existingPromise) {

      return await existingPromise;
    }

    // Create a new promise for this user's agent creation and cache it
    const agentCreationPromise = createAgentForUser(userEmail, apiKey);
    agentCreationCache.set(userEmail, agentCreationPromise);

    try {
      const result = await agentCreationPromise;
      return result;
    } finally {
      // Always remove from cache when done (success or failure)
      agentCreationCache.delete(userEmail);
    }
  } catch (error: any) {
    console.error(`[USER_AGENT_MANAGER] Error ensuring agent for ${userEmail}:`, error);
    agentCreationCache.delete(userEmail); // Clean up cache on error
    return {
      success: false,
      error: `Error ensuring agent: ${error.message}`
    };
  }
}

/**
 * Internal function to handle the actual agent creation logic
 * @param userEmail - Email of the user
 * @param apiKey - Optional ElevenLabs API key
 * @returns Agent creation/verification result
 */
async function createAgentForUser(userEmail: string, apiKey?: string): Promise<AgentCreationResult> {


  // Step 1: Check if user already has an agent ID stored
  let existingAgentId = await getUserAgentId(userEmail);

  if (existingAgentId) {


    // Step 2: Verify the agent still exists in ElevenLabs
    const agentExists = await checkAgentExists(existingAgentId, apiKey);

    if (agentExists.exists) {


      // CRITICAL: Validate and fix existing agent configuration

      const configValidation = await validateAndFixUserAgentConfig(existingAgentId, userEmail, apiKey);

      if (!configValidation.success) {
     } else {

        if (configValidation.configurationFixed) {
  
        }
      }

      // Update last check timestamp
      await storeUserAgentData(userEmail, {
        elevenLabsAgentId: existingAgentId,
        agentCreatedAt: new Date().toISOString(), // We don't have the original date, so use current
        agentStatus: 'active',
        lastAgentCheck: new Date().toISOString(),
      });

      return { success: true, agentId: existingAgentId, isNewAgent: false };
    } else {

      // Agent was deleted or doesn't exist, we'll create a new one
      existingAgentId = null;
    }
  }

  // Step 3: Create a new agent


  const newAgentId = generateUserAgentId(userEmail);

  try {
    // Update status to creating
    await storeUserAgentData(userEmail, {
      elevenLabsAgentId: newAgentId,
      agentCreatedAt: new Date().toISOString(),
      agentStatus: 'creating',
      lastAgentCheck: new Date().toISOString(),
      agentCreationAttempts: 1,
    });

    const agentResponse = await createUserAgent(newAgentId, userEmail, apiKey);

    // CRITICAL: Validate and fix agent configuration to ensure text response transmission

    const configValidation = await validateAndFixUserAgentConfig(agentResponse.agent_id, userEmail, apiKey);

    if (!configValidation.success) {
      console.error(`[USER_AGENT_MANAGER] ❌ Agent configuration validation failed:`, configValidation.error);
      // Don't fail the entire creation, but log the issue
      console.warn(`[USER_AGENT_MANAGER] ⚠️ Agent created but configuration may be incomplete. Text responses may not work properly.`);
    } else {

    }

    // Update status to active
    await storeUserAgentData(userEmail, {
      elevenLabsAgentId: agentResponse.agent_id,
      agentCreatedAt: agentResponse.created_at,
      agentStatus: 'active',
      lastAgentCheck: new Date().toISOString(),
      agentCreationAttempts: 1,
    });



    return {
      success: true,
      agentId: agentResponse.agent_id,
      isNewAgent: true
    };
  } catch (createError: any) {
    console.error(`[USER_AGENT_MANAGER] ❌ Failed to create agent for ${userEmail}:`, createError);

    // Update status to failed
    try {
      await storeUserAgentData(userEmail, {
        elevenLabsAgentId: newAgentId,
        agentCreatedAt: new Date().toISOString(),
        agentStatus: 'failed',
        lastAgentCheck: new Date().toISOString(),
        agentCreationAttempts: 1,
      });
    } catch (storeError) {
      console.error(`[USER_AGENT_MANAGER] Error updating failed status:`, storeError);
    }

    return {
      success: false,
      error: `Failed to create agent: ${createError.message}`
    };
  }
}

/**
 * Gets a user's agent ID, creating one if it doesn't exist
 * This is the main function components should use
 * @param userEmail - Email of the user
 * @param apiKey - Optional ElevenLabs API key
 * @returns Agent ID or null if creation failed
 */
/**
 * Validates and fixes a user's agent configuration to ensure proper WebSocket message flow
 * This function can be called independently to check and fix existing agents
 * @param userEmail - Email of the user
 * @param apiKey - Optional ElevenLabs API key
 * @returns Object indicating validation results and any fixes applied
 */
export async function validateUserAgentConfiguration(userEmail: string, apiKey?: string): Promise<{
  success: boolean;
  agentId?: string;
  configurationFixed: boolean;
  error?: string;
  details?: string;
}> {
  try {
    if (!userEmail || typeof userEmail !== 'string') {
      return {
        success: false,
        configurationFixed: false,
        error: "User email is required and must be a string"
      };
    }



    // Step 1: Get the user's agent ID
    const existingAgentId = await getUserAgentId(userEmail);
    if (!existingAgentId) {
      return {
        success: false,
        configurationFixed: false,
        error: "No agent found for user. Please create an agent first."
      };
    }

    // Step 2: Validate and fix the agent configuration
    const configValidation = await validateAndFixUserAgentConfig(existingAgentId, userEmail, apiKey);

    return {
      success: configValidation.success,
      agentId: existingAgentId,
      configurationFixed: configValidation.configurationFixed,
      error: configValidation.error,
      details: configValidation.details
    };

  } catch (error: any) {
    console.error(`[USER_AGENT_VALIDATION] ❌ Error validating user agent configuration for ${userEmail}:`, error);
    return {
      success: false,
      configurationFixed: false,
      error: `Validation failed: ${error?.message || "Unknown error"}`
    };
  }
}

export default async function getOrCreateUserAgent(userEmail: string, apiKey?: string): Promise<string | null> {
  try {
    const result = await ensureUserAgent(userEmail, apiKey);

    if (result.success && result.agentId) {
      return result.agentId;
    } else {
      console.error(`[USER_AGENT_MANAGER] Failed to get or create agent for ${userEmail}:`, result.error);
      return null;
    }
  } catch (error) {
    console.error(`[USER_AGENT_MANAGER] Error in getOrCreateUserAgent for ${userEmail}:`, error);
    return null;
  }
}
