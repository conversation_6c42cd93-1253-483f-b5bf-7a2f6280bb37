import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { validateUserAgentConfiguration } from '../../../lib/userAgentManager';

/**
 * POST /api/validate-agent-config
 * Validates and fixes user agent configuration for proper WebSocket message flow
 */
export async function POST(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    //console.log(`[API_VALIDATE_AGENT] Starting validation for user: ${session.user.email}`);

    const result = await validateUserAgentConfiguration(session.user.email);

    if (!result.success) {
      console.error(`[API_VALIDATE_AGENT] Validation failed for ${session.user.email}:`, result.error);
      return NextResponse.json(
        {
          success: false,
          error: result.error,
          userEmail: session.user.email
        },
        { status: 500 }
      );
    }

    //console.log(`[API_VALIDATE_AGENT] Validation completed for ${session.user.email}:`, {
    //  agentId: result.agentId,
    //  configurationFixed: result.configurationFixed,
    //  details: result.details
    //});

    return NextResponse.json({
      success: true,
      agentId: result.agentId,
      configurationFixed: result.configurationFixed,
      details: result.details,
      userEmail: session.user.email,
      message: result.configurationFixed
        ? 'Agent configuration was updated to enable text response transmission'
        : 'Agent configuration was already correct'
    });

  } catch (error) {
    console.error('[API_VALIDATE_AGENT] Error during validation:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during validation'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/validate-agent-config
 * Returns the current validation status without making changes
 */
export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      message: 'Agent configuration validation endpoint is available',
      userEmail: session.user.email,
      instructions: 'Use POST method to validate and fix agent configuration'
    });

  } catch (error) {
    console.error('[API_VALIDATE_AGENT] Error in GET endpoint:', error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
