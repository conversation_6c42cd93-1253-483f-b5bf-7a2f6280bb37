import { LLMDataProcessingTool } from "components/tools/LLMDataProcessingTool";

// ======================
// Type Definitions
// ======================

interface NormalizedDataPoint extends Record<string, unknown> {
  time?: string;
  metrics: Record<string, number>;
  categories: string[];
}

interface NormalizedMetadata {
  type: 'time-series' | 'categorical' | 'distribution' | 'comparison';
  metrics: string[];
  timeUnit?: 'day' | 'month' | 'quarter' | 'year' | null;
}

interface ProcessedResponse {
  data: NormalizedDataPoint[];
  metadata: NormalizedMetadata;
  timeMatches: Array<{
    value: string;
    type: string;
    originalFormat: string;
  }>;
}

interface VisualizationRequest {
  query: string;
  data: any[];
  options: {
    type: 'chart' | 'table';
    features: string[];
    formatting: Record<string, string>;
  };
}

interface VisualizationResponse {
  success: boolean;
  error?: string;
  visualization?: {
    type: 'chart' | 'table';
    subtype: string;
    config: ChartConfig;
    data: any[];
    metadata: NormalizedMetadata;
    confidence: number;
    reasoning: string;
  };
}

interface AxisConfig {
  key: string;
  label?: string; // So we can define an X-axis label
}

interface YAxisConfig extends AxisConfig {
  domain: [number, number];
  tickCount: number;
  formatType?: 'number' | 'currency' | 'percentage' | 'compact';
}

interface ChartConfig {
  type: string;
  xAxis: AxisConfig;
  yAxis: YAxisConfig;
  series: string[];
  stacked: boolean;
  colors: string[];
  timeUnit?: string | null;
}

interface VisualizationAgentConfig {
  maxResults?: number;
  defaultVisualizationType?: 'chart' | 'table';
}

interface TransformedDataPoint {
  time: string;
  [key: string]: number | string;
}

// ======================
// VisualizationAgent Implementation
// ======================

export class VisualizationAgent {
  private maxResults: number;
  private defaultVisualizationType: 'chart' | 'table';

  constructor(config?: VisualizationAgentConfig) {
    this.maxResults = config?.maxResults || 100;
    this.defaultVisualizationType = config?.defaultVisualizationType || 'chart';
  }

  private validateDataStructure(data: any): boolean {
    try {
      if (!data || typeof data !== 'object') return false;
      if (!Array.isArray(data.data)) return false;
      if (!data.metadata || typeof data.metadata !== 'object') return false;
      if (!Array.isArray(data.timeMatches)) return false;

      const validDataPoints = data.data.every((point: NormalizedDataPoint) => {
        return (
          typeof point === 'object' &&
          typeof point.metrics === 'object' &&
          Array.isArray(point.categories)
        );
      });

      return validDataPoints;
    } catch (error) {
      console.error('Data structure validation failed:', error);
      return false;
    }
  }

  /**
   * Main process method: calls the LLMDataProcessingTool, transforms data, and builds final chart config
   */
  public async process(request: VisualizationRequest): Promise<VisualizationResponse> {
    try {
      const llmTool = new LLMDataProcessingTool(process.env.GROQ_API_KEY || '');
      const processedResponse = await llmTool.call({
        content: request.query,
        options: {
          includeTimeAnalysis: true
        }
      });

      if (!this.validateDataStructure(processedResponse)) {
        throw new Error('Invalid data structure received from LLMDataProcessingTool');
      }

      // Flatten the data for Recharts
      const flattenedVisualizationData = this.transformDataForVisualization(processedResponse);

      // If the dataset is large, truncate
      const chartData = flattenedVisualizationData.content.data;
      if (chartData.length > this.maxResults) {
        chartData.splice(this.maxResults);
      }

      // Build final ChartConfig
      const chartConfig = this.generateChartConfig(
        chartData,
        processedResponse.metadata,
        processedResponse.metadata.type === 'comparison'
      );

      // Decide chart vs. table
      const finalType = request.options.type || this.defaultVisualizationType;

      return {
        success: true,
        visualization: {
          type: finalType,
          subtype: processedResponse.metadata.type,
          config: chartConfig,
          data: chartData,
          metadata: processedResponse.metadata,
          confidence: 1,
          reasoning: `Data successfully processed and validated for ${processedResponse.metadata.type} visualization`
        }
      };

    } catch (error) {
      console.error('Visualization processing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error in visualization processing'
      };
    }
  }

  /**
   * Flatten the LLM data into a Recharts-friendly structure
   */
  private transformDataForVisualization(processedResponse: ProcessedResponse): {
    type: 'visualization';
    content: {
      type: 'chart';
      subtype: string;
      config: {
        type: string;
        xAxis: string;
        series: string[];
        stacked: boolean;
      };
      data: TransformedDataPoint[];
      metadata: NormalizedMetadata;
    };
  } {
    // Flatten each point's metrics into top-level keys
    const transformedData = processedResponse.data.map((point) => {
      const transformed: TransformedDataPoint = {
        time: point.time || ''
      };
      Object.entries(point.metrics).forEach(([key, value]) => {
        transformed[key] = value;
      });
      return transformed;
    });

    return {
      type: 'visualization',
      content: {
        type: 'chart',
        subtype: processedResponse.metadata.type,
        config: {
          type: this.mapChartType(processedResponse.metadata.type),
          xAxis: 'time', // We'll supply a label in generateChartConfig
          series: processedResponse.metadata.metrics,
          stacked: processedResponse.metadata.type === 'comparison'
        },
        data: transformedData,
        metadata: processedResponse.metadata
      }
    };
  }

  /**
   * Convert the flattened data into a full ChartConfig (including xAxis.label and yAxis.label)
   */
  private generateChartConfig(
    data: TransformedDataPoint[],
    metadata: NormalizedMetadata,
    isStacked: boolean
  ): ChartConfig {
    const yAxisDomain = this.calculateYAxisDomain(data);
    const tickCount = this.determineOptimalTickCount(yAxisDomain);

    // Build final chart config
    return {
      type: this.mapChartType(metadata.type),
      xAxis: {
        key: 'time',
        // The crucial line: always set the X-axis label
        label: metadata.timeUnit ? `Time (${metadata.timeUnit})` : 'Time'
      },
      yAxis: {
        key: 'value',
        domain: yAxisDomain,
        tickCount,
        // Also set the Y-axis label
        label: metadata.metrics.length > 1 ? 'Value' : metadata.metrics[0],
        formatType: 'compact'
      },
      series: metadata.metrics,
      stacked: isStacked,
      colors: metadata.metrics.map(() => this.getRandomColor()),
      timeUnit: metadata.timeUnit
    };
  }

  /**
   * Scan data to find min/max for Y-axis
   */
  private calculateYAxisDomain(data: TransformedDataPoint[]): [number, number] {
    if (!Array.isArray(data) || data.length === 0) {
      return [0, 100];
    }

    let minValue = Number.POSITIVE_INFINITY;
    let maxValue = Number.NEGATIVE_INFINITY;

    data.forEach(point => {
      Object.entries(point)
        .filter(([key, val]) => key !== 'time' && typeof val === 'number')
        .forEach(([_, val]) => {
          minValue = Math.min(minValue, val as number);
          maxValue = Math.max(maxValue, val as number);
        });
    });

    if (minValue === Number.POSITIVE_INFINITY || maxValue === Number.NEGATIVE_INFINITY) {
      return [0, 100];
    }

    const padding = (maxValue - minValue) * 0.1;
    return [
      Math.max(0, minValue - padding),
      maxValue + padding
    ];
  }

  /**
   * Decide how many tick lines to show on Y-axis
   */
  private determineOptimalTickCount(domain: [number, number]): number {
    const range = domain[1] - domain[0];
    if (range <= 0) return 5;
    if (range <= 10) return Math.ceil(range) + 1;
    if (range <= 20) return 10;
    return 5;
  }

  /**
   * Choose chart type based on metadata
   */
  private mapChartType(type: string): string {
    switch (type) {
      case 'time-series':
        return 'line';  // or 'bar' if you want time-series as bar
      case 'categorical':
        return 'bar';
      case 'distribution':
        return 'scatter';
      case 'comparison':
        return 'bar';
      default:
        return 'line';
    }
  }

  /**
   * Generate a random color for each series
   */
  private getRandomColor(): string {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }
}
