'use client'

import React, { useState, useEffect } from 'react';
import { BarC<PERSON>, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { MessageCircle, FileText, Search, Zap, BookOpen } from 'lucide-react';
import { collection, query, orderBy, limit, getDocs, DocumentData } from 'firebase/firestore';
import { db } from 'components/firebase';

interface ProcessMetrics {
  averageRelevance: number;
  chatHistoryTokens: number;
  chunkCount: number;
  contextLength?: number;
  metadata: string;
  namespaceDistribution: Record<string, number>;
  systemPromptTokens: number;
  timestamp: string;
  totalTokens: number;
}

interface Source {
  title: string;
  page: number;
  relevance: number;
}

interface TokenUsage {
  contextTokens: number;
  systemPromptTokens: number;
  chatHistoryTokens: number;
  totalTokens: number;
}

interface ProcessedMetadata {
  sources: Source[];
  tokenUsage: TokenUsage;
  averageRelevance: number;
}

interface QueryMetrics {
  historyLength: number;
  contextLength: number;
  queryLength: number;
}

const parseMetadataString = (metadataStr: string): ProcessedMetadata => {
  const lines = metadataStr.split('\n');
  const sources: Source[] = [];
  const tokenUsage: TokenUsage = {
    contextTokens: 0,
    systemPromptTokens: 0,
    chatHistoryTokens: 0,
    totalTokens: 0
  };
  let averageRelevance = 0;
  let currentSection = '';

  lines.forEach(line => {
    if (line.startsWith('Sources:')) {
      currentSection = 'sources';
    } else if (line.startsWith('Token Usage:')) {
      currentSection = 'tokens';
    } else if (line.startsWith('Average Relevance:')) {
      averageRelevance = parseFloat(line.split(':')[1].trim());
    } else if (line.startsWith('-')) {
      if (currentSection === 'sources') {
        const match = line.match(/- (.+) \(Page (\d+)\) - Relevance: ([\d.]+)/);
        if (match) {
          sources.push({
            title: match[1],
            page: parseInt(match[2]),
            relevance: parseFloat(match[3])
          });
        }
      } else if (currentSection === 'tokens') {
        const [key, value] = line.substring(2).split(':').map(s => s.trim());
        const tokenCount = parseInt(value);
        switch (key) {
          case 'Context Tokens':
            tokenUsage.contextTokens = tokenCount;
            break;
          case 'System Prompt Tokens':
            tokenUsage.systemPromptTokens = tokenCount;
            break;
          case 'Chat History Tokens':
            tokenUsage.chatHistoryTokens = tokenCount;
            break;
          case 'Total Tokens':
            tokenUsage.totalTokens = tokenCount;
            break;
        }
      }
    }
  });

  return {
    sources,
    tokenUsage,
    averageRelevance
  };
};

const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500" />
  </div>
);

export default function MetricsDashboard() {
  const [metrics, setMetrics] = useState<ProcessedMetadata | null>(null);
  const [queryMetrics, setQueryMetrics] = useState<QueryMetrics>({
    historyLength: 0,
    contextLength: 0,
    queryLength: 0
  });
  const [loading, setLoading] = useState(true);
  const [latestDocId, setLatestDocId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLatestMetrics = async () => {
      try {
        setError(null);
        
        // Query to get the latest document based on timestamp
        const metricsQuery = query(
          collection(db, 'process_metrics'),
          orderBy('timestamp', 'desc'),
          limit(1)
        );

        const querySnapshot = await getDocs(metricsQuery);
        
        if (!querySnapshot.empty) {
          const latestDoc = querySnapshot.docs[0];
          const newDocId = latestDoc.id;
          
          // Only update if we have a new document
          if (newDocId !== latestDocId) {
            //console.log('New document detected:', newDocId);
            setLatestDocId(newDocId);

            const data = latestDoc.data() as ProcessMetrics;
            
            // Parse the metadata string
            const parsedMetadata = parseMetadataString(data.metadata);
            setMetrics(parsedMetadata);
            
            // Set query metrics
            setQueryMetrics({
              historyLength: data.chatHistoryTokens || 0,
              contextLength: data.contextLength || parsedMetadata.tokenUsage.contextTokens || 0,
              queryLength: 0
            });
          }
        } else {
          setError('No metrics data available');
          //console.log('No documents found in process_metrics collection');
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An error occurred';
        setError(`Error fetching metrics: ${errorMessage}`);
        console.error('Error fetching metrics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLatestMetrics();
    const intervalId = setInterval(fetchLatestMetrics, 10000);
    
    return () => clearInterval(intervalId);
  }, [latestDocId]);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500 text-center">
          <h2 className="text-xl font-bold mb-2">Error</h2>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-gray-500 text-center">
          <h2 className="text-xl font-bold mb-2">No Data</h2>
          <p>No metrics data is currently available</p>
        </div>
      </div>
    );
  }

  const tokenUsageData = [
    { name: 'Context', tokens: metrics.tokenUsage.contextTokens },
    { name: 'System Prompt', tokens: metrics.tokenUsage.systemPromptTokens },
    { name: 'Chat History', tokens: metrics.tokenUsage.chatHistoryTokens }
  ];

  return (
    <div className="p-4 space-y-4 bg-gray-100">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Query Analysis Dashboard</h1>
        <div className="flex flex-col sm:flex-row items-end sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
          <div className="text-sm text-gray-500">
            Document ID: {latestDocId || 'Unknown'}
          </div>
          <div className="text-sm text-gray-500">
            Auto-refreshing every 10s
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">Total Tokens Used</h2>
            <BookOpen className="h-5 w-5 text-blue-500" />
          </div>
          <div className="text-4xl font-bold text-blue-600 mb-2">
            {metrics.tokenUsage.totalTokens.toLocaleString()}
          </div>
          <div className="grid grid-cols-3 gap-4 mt-4">
            <div>
              <div className="text-sm text-gray-500">Context</div>
              <div className="text-lg font-semibold text-gray-800">
                {metrics.tokenUsage.contextTokens.toLocaleString()}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-500">System</div>
              <div className="text-lg font-semibold text-gray-800">
                {metrics.tokenUsage.systemPromptTokens.toLocaleString()}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-500">History</div>
              <div className="text-lg font-semibold text-gray-800">
                {metrics.tokenUsage.chatHistoryTokens.toLocaleString()}
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">Source Documents</h2>
            <div className="text-sm text-gray-500">
              Average Relevance: {(metrics.averageRelevance).toFixed(2)}%
            </div>
          </div>
          <div className="space-y-4">
            {metrics.sources.map((source, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium truncate max-w-xs">
                    {source.title} (Page {source.page})
                  </span>
                  <span className="text-sm font-medium text-blue-600">
                    {(source.relevance).toFixed(2)}%
                  </span>
                </div>
                <div className="w-full bg-gray-100 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${source.relevance}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-sm font-medium text-gray-800">History Length</h2>
            <MessageCircle className="h-4 w-4 text-blue-500" />
          </div>
          <div className="text-2xl font-bold text-blue-600">{queryMetrics.historyLength}</div>
          <p className="text-xs text-gray-500 mt-1">Messages in conversation history</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-sm font-medium text-gray-800">Context Length</h2>
            <FileText className="h-4 w-4 text-blue-500" />
          </div>
          <div className="text-2xl font-bold text-blue-600">{queryMetrics.contextLength}</div>
          <p className="text-xs text-gray-500 mt-1">Characters in current context</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-sm font-medium text-gray-800">Query Length</h2>
            <Search className="h-4 w-4 text-blue-500" />
          </div>
          <div className="text-2xl font-bold text-blue-600">{queryMetrics.queryLength}</div>
          <p className="text-xs text-gray-500 mt-1">Characters in current query</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-sm font-medium text-gray-800">Overall Relevance</h2>
            <Zap className="h-4 w-4 text-blue-500" />
          </div>
          <div className="text-2xl font-bold text-blue-600 mb-2">
            {(metrics.averageRelevance).toFixed(2)}%
          </div>
          <div className="w-full bg-gray-100 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${metrics.averageRelevance}%` }}
            />
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-lg font-semibold mb-4 text-gray-800">Token Distribution</h2>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={tokenUsageData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#eee" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip 
                formatter={(value) => [`${value} tokens`, 'Usage']}
                contentStyle={{ backgroundColor: 'white', border: '1px solid #e2e8f0' }}
              />
              <Legend />
              <Bar dataKey="tokens" fill="#3b82f6" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}