import { Pinecone } from '@pinecone-database/pinecone';

// Function to delete all vectors in a Pinecone namespace
export const deleteEmbeddingsFromPinecone = async (namespace: string, vectorIds: string[]) => {
  try {
    const pinecone = new Pinecone({ apiKey: process.env.PINECONE_API_KEY! });
    const index = pinecone.index(process.env.PINECONE_INDEX!);

    const ns = index.namespace(namespace);

    if (vectorIds.length > 0) {
      // Delete all vectors by their IDs
      await ns.deleteMany(vectorIds);
      //console.log(`All vectors within namespace: ${namespace} have been deleted.`);
    } else {
      //console.log(`No vectors found in the namespace: ${namespace}`);
    }
  } catch (error) {
    console.error('Error deleting vectors from Pinecone namespace:', error);
    throw new Error('Error deleting vectors from Pinecone namespace.');
  }
};

