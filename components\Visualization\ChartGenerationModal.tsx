// ChartGenerationModal.tsx
import React, { useEffect, useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  Timestamp,
} from 'firebase/firestore';
import { db } from 'components/firebase';
import { X, BarChart, Loader2, XIcon, ThumbsUpIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import VisualizationRenderer from './VisualizationRenderer';

// Type definitions
interface ChartGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onChartGenerated: (chartUrl: string) => void;
}

interface SavedChart {
  id: string;
  url: string;
  description: string;
  type: string;
  createdAt: Timestamp;
}

interface ProgressState {
  stage: 'initializing' | 'processing' | 'generating' | 'indexing' | 'complete' | 'error';
  message: string;
  progress?: number;
}

// Constants
const PROGRESS_MESSAGES = {
  initializing: 'Initializing chart generation...',
  processing: 'Processing data for visualization...',
  generating: 'Generating your chart with AI...',
  indexing: 'Indexing chart for search and analysis...',
  complete: 'Chart generation complete!',
  error: 'An error occurred',
} as const;

const MAX_PROMPT_LENGTH = 1000;
const MAX_SAVED_CHARTS = 5;

// Helper Components
const ProgressIndicator = ({ progress }: { progress: ProgressState }) => (
  <div className="flex flex-col items-center gap-4">
    <div className="w-full max-w-md bg-gray-700 rounded-full h-2.5 overflow-hidden">
      <div 
        className={`h-2.5 rounded-full ${
          progress.stage === 'error' ? 'bg-red-500' : 'bg-blue-500'
        } transition-all duration-500 ease-in-out`}
        style={{ 
          width: `${
            progress.stage === 'complete' 
              ? '100' 
              : progress.stage === 'initializing' 
              ? '25%'
              : progress.stage === 'processing'
              ? '50%'
              : progress.stage === 'generating'
              ? '75%'
              : '0%'
          }` 
        }}
      />
    </div>
    <div className="flex items-center gap-2">
      {progress.stage === 'error' ? (
        <XIcon className="w-6 h-6 text-red-500" />
      ) : progress.stage === 'complete' ? (
        <ThumbsUpIcon className="w-6 h-6 text-green-500" />
      ) : (
        <Loader2 className="w-6 h-6 text-blue-400 animate-spin" />
      )}
      <span
        className={`
          ${progress.stage === 'error' ? 'text-red-500' : 'text-blue-400'}
          text-sm font-medium
        `}
      >
        {progress.message}
      </span>
    </div>
  </div>
);

const SavedChartPreview = ({ chart }: { chart: SavedChart }) => (
  <div className="relative group">
    <div className="w-24 h-24 border-2 border-gray-600 bg-[#1a1a1a] rounded-lg flex items-center justify-center transition-transform duration-200 group-hover:scale-105">
      <BarChart className="w-12 h-12 text-gray-400" />
    </div>
    <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 truncate">
      {chart.type}
    </div>
    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
      <span className="text-white text-xs text-center px-2">
        {chart.description.length > 50 
          ? `${chart.description.substring(0, 50)}...` 
          : chart.description}
      </span>
    </div>
  </div>
);

const PlaceholderChart = () => (
  <div className="flex items-center justify-center w-24 h-24 border-2 border-gray-600 bg-[#1a1a1a] rounded-lg">
    <span className="text-gray-500 text-sm">Empty Slot</span>
  </div>
);

// Updated VisualizationData structure to include 'pie'
interface VisualizationData {
  type: 'visualization';
  content: {
    type: 'chart';
    subtype: string;
    config: {
      type: string;
      xAxis?: string;
      yAxis?: string;
      series?: string[];
      colors?: string[];
      stacked?: boolean;
    };
    data: any[];
    metadata: {
      type: 'time-series' | 'categorical' | 'distribution' | 'comparison' | 'pie';
      metrics: string[];
      timeUnit?: 'day' | 'month' | 'quarter' | 'year' | null;
    };
    confidence: number;
    reasoning: string;
  };
}

export default function ChartGenerationModal({
  isOpen,
  onClose,
  onChartGenerated,
}: ChartGenerationModalProps) {
  // Hooks
  const { data: session } = useSession();
  const router = useRouter();
  const userId = session?.user?.email ?? 'anonymous';

  // State
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedChartData, setGeneratedChartData] = useState<VisualizationData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [savedCharts, setSavedCharts] = useState<SavedChart[]>([]);
  const [progress, setProgress] = useState<ProgressState>({
    stage: 'initializing',
    message: PROGRESS_MESSAGES.initializing
  });

  // Effects
  useEffect(() => {
    if (!isOpen || !userId || userId === 'anonymous') return;

    const qRef = query(
      collection(db, 'users', userId, 'files'),
      where('category', '==', 'Charts'),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(qRef, (snapshot) => {
      const charts: SavedChart[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        if (data.downloadUrl) {
          charts.push({
            id: doc.id,
            url: data.downloadUrl,
            description: data.description || 'No description available',
            type: data.type || 'Unknown Chart Type',
            createdAt: data.createdAt
          });
        }
      });
      setSavedCharts(charts.slice(0, MAX_SAVED_CHARTS));
    }, (error) => {
      console.error('Error fetching saved charts:', error);
      setError('Failed to load saved charts');
    });

    return () => unsubscribe();
  }, [userId, isOpen]);

  // Logging generatedChartData using useEffect
  useEffect(() => {
    if (generatedChartData) {
      //console.log('[DEBUG] final VisualizationData config:', generatedChartData.content.config);
    }
  }, [generatedChartData]);

  // Handlers
  const handlePromptChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= MAX_PROMPT_LENGTH) {
      setPrompt(newValue);
      setError(null);
    }
  }, []);

  const handleGenerateChart = async () => {
    if (!prompt.trim()) {
      setError('Please provide a description for your chart.');
      return;
    }

    if (!userId || userId === 'anonymous') {
      setError('Please sign in to generate charts.');
      return;
    }

    setIsGenerating(true);
    setError(null);
    setGeneratedChartData(null); // Reset to null to clear previous chart
    setProgress({ stage: 'initializing', message: PROGRESS_MESSAGES.initializing });

    try {
      // 1. Initialize chart generation
      const initResponse = await fetch('/api/initializeChartGeneration', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt, userId }),
      });

      if (!initResponse.ok) {
        const errorData = await initResponse.json();
        throw new Error(errorData.error || 'Failed to initialize chart generation');
      }

      const initData = await initResponse.json();
      if (!initData.success || !initData.jobId) {
        throw new Error(initData.error || 'No job ID received');
      }

      setProgress({ stage: 'processing', message: PROGRESS_MESSAGES.processing });

      // 2. Process chart generation
      const processResponse = await fetch('/api/processChartGeneration', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ jobId: initData.jobId, userId }),
      });

      if (!processResponse.ok) {
        const errorData = await processResponse.json();
        throw new Error(errorData.error || 'Failed to process chart');
      }

      const result = await processResponse.json();
      if (!result.success || !result.chartUrl) {
        throw new Error(result.error || 'Failed to generate chart');
      }

      // 3. Fetch chart data
      const chartDataResponse = await fetch(result.chartUrl);
      if (!chartDataResponse.ok) {
        throw new Error('Failed to fetch chart data');
      }

      const chartData = await chartDataResponse.json();

      // Debugging logs are now handled in useEffect

      // 4. Transform the chart data
      const transformedChartData: VisualizationData = {
        type: 'visualization',
        content: {
          type: 'chart',
          subtype: chartData.subtype,
          config: {
            type: chartData.config.type,
            // Conditionally include xAxis and yAxis only if the chart type requires them
            ...(chartData.config.type !== 'pie' && {
              xAxis: chartData.config.xAxis,
              yAxis: chartData.config.yAxis,
            }),
            series: chartData.config.series,
            colors: chartData.config.colors,
            stacked: chartData.config.stacked,
          },
          data: chartData.data,
          metadata: {
            type: chartData.metadata.type, // Ensure 'pie' is included
            metrics: chartData.metadata.metrics,
            timeUnit: chartData.metadata.timeUnit,
          },
          confidence: chartData.confidence,
          reasoning: chartData.reasoning
        }
      };

      setGeneratedChartData(transformedChartData);
      onChartGenerated(result.chartUrl);
      setProgress({ stage: 'complete', message: PROGRESS_MESSAGES.complete });

    } catch (err) {
      console.error('Chart generation error:', err);
      setProgress({ 
        stage: 'error', 
        message: err instanceof Error ? err.message : 'An error occurred during chart generation'
      });
      setError(err instanceof Error ? err.message : 'Failed to generate chart');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleViewAllCharts = useCallback(() => {
    router.push('/chartGallery');
  }, [router]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="bg-[#2d1d3a] rounded-lg p-6 w-full max-w-4xl mx-auto relative my-auto">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-gray-400 hover:text-white transition-colors duration-200"
          aria-label="Close modal"
        >
          <X className="w-6 h-6" />
        </button>

        <div className="flex gap-4 mb-6 items-center ">
          {savedCharts.length === 0 ? (
            Array.from({ length: MAX_SAVED_CHARTS }).map((_, idx) => (
              <PlaceholderChart key={`placeholder-${idx}`} />
            ))
          ) : (
            <>
              {savedCharts.map((chart) => (
                <SavedChartPreview key={chart.id} chart={chart} />
              ))}
              {savedCharts.length < MAX_SAVED_CHARTS && 
                Array.from({ length: MAX_SAVED_CHARTS - savedCharts.length }).map((_, idx) => (
                  <PlaceholderChart key={`placeholder-${idx}`} />
                ))
              }
            </>
          )}
          
          <button
            onClick={handleViewAllCharts}
            className="ml-auto px-4 py-2 bg-indigo-500 text-white text-sm rounded-lg hover:bg-indigo-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-[#2d1d3a]"
          >
            View all charts
          </button>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          {/* 
            Increased the container size to ensure there's enough vertical space for the X-axis label.
          */}
          <div className="flex-1 border-2 border-dashed border-gray-600 rounded-lg bg-[#1a1a1a] flex items-center justify-center relative h-[600px]">
            {isGenerating ? (
              <div className="flex flex-col items-center justify-center h-full">
                <ProgressIndicator progress={progress} />
              </div>
            ) : generatedChartData ? (
              <VisualizationRenderer data={generatedChartData} />
            ) : (
              <div className="flex flex-col items-center justify-center gap-2 text-gray-500">
                <BarChart className="w-12 h-12" />
                <span className="text-sm">Your chart will appear here</span>
              </div>
            )}
          </div>

          <div className="w-full md:w-72 flex flex-col gap-2">
            <textarea
              value={prompt}
              onChange={handlePromptChange}
              placeholder="Describe the chart you want to generate..."
              className="w-full h-full min-h-[200px] md:min-h-[400px] bg-[#1a1a1a] border-2 border-gray-600 rounded-lg p-4 text-xs text-gray-200 resize-none focus:outline-none focus:border-blue-500 transition-colors duration-200"
              maxLength={MAX_PROMPT_LENGTH}
              disabled={isGenerating}
            />
            <div className="text-xs text-gray-400 text-right">
              {prompt.length}/{MAX_PROMPT_LENGTH}
            </div>
          </div>
        </div>

        {error && (
          <div className="mt-4 px-4 py-3 bg-red-500 bg-opacity-10 border border-red-500 rounded-lg">
            <p className="text-red-500 text-sm flex items-center gap-2">
              <XIcon className="w-4 h-4" />
              {error}
            </p>
          </div>
        )}

        <button
          onClick={handleGenerateChart}
          disabled={isGenerating || !prompt.trim() || userId === 'anonymous'}
          className="mt-6 w-full bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#2d1d3a]"
        >
          {isGenerating ? (
            <>
              <Loader2 className="w-5 h-5 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <BarChart className="w-5 h-5" />
              Generate Chart
            </>
          )}
        </button>
      </div>
    </div>
  );
}
