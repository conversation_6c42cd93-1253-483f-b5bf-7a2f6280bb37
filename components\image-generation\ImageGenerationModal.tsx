'use client';

import React, { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
} from 'firebase/firestore';
import { db } from 'components/firebase';
import { X, ImageIcon, Loader2, XIcon, ThumbsUpIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ImageGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImageGenerated: (imageUrl: string) => void;
}

interface SavedImage {
  url: string;
  description: string;
}

interface ImageGenerationResponse {
  success: boolean;
  imageUrl: string;
  namespace: string;
  jobId: string;
}

interface ProgressState {
  stage: 'initializing' | 'generating' | 'processing' | 'indexing' | 'complete' | 'error';
  message: string;
  progress?: number;
}

const PROGRESS_MESSAGES = {
  initializing: 'Initializing image generation...',
  generating: 'Generating your image with AI...',
  processing: 'Processing and storing image data...',
  indexing: 'Indexing image for search and analysis...',
  complete: 'Image generation complete!',
  error: 'An error occurred',
};

const ProgressIndicator = ({ progress }: { progress: ProgressState }) => {
  return (
    <div className="flex flex-col items-center gap-4">
      <div className="w-full max-w-md bg-gray-700 rounded-full h-2.5">
        <div 
          className={`h-2.5 rounded-full ${
            progress.stage === 'error' ? 'bg-red-500' : 'bg-blue-500'
          } transition-all duration-500 ease-in-out`}
          style={{ 
            width: `${
              progress.stage === 'complete' 
                ? '100' 
                : progress.stage === 'initializing' 
                ? '25'
                : progress.stage === 'generating'
                ? '50'
                : progress.stage === 'indexing'
                ? '75'
                : '0'
            }%` 
          }}
        />
      </div>
      <div className="flex items-center gap-2">
        {progress.stage === 'error' ? (
          <XIcon className="w-6 h-6 text-red-500" />
        ) : progress.stage === 'complete' ? (
          <ThumbsUpIcon className="w-6 h-6 text-green-500" />
        ) : (
          <Loader2 className="w-6 h-6 text-blue-400 animate-spin" />
        )}
        <span className={`
          ${progress.stage === 'error' ? 'text-red-500' : 'text-blue-400'}
          text-sm font-medium
        `}>
          {progress.message}
        </span>
      </div>
    </div>
  );
};

export default function ImageGenerationModal({
  isOpen,
  onClose,
  onImageGenerated,
}: ImageGenerationModalProps) {
  const { data: session } = useSession();
  const userId = session?.user?.email ?? 'anonymous';

  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImageUrl, setGeneratedImageUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [savedImages, setSavedImages] = useState<SavedImage[]>([]);
  const [progress, setProgress] = useState<ProgressState>({
    stage: 'initializing',
    message: PROGRESS_MESSAGES.initializing
  });

  const router = useRouter();

  useEffect(() => {
    if (!isOpen || !userId) return;

    const qRef = query(
      collection(db, 'users', userId, 'files'),
      where('category', '==', 'My Images'),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(qRef, (snapshot) => {
      const allImages: SavedImage[] = [];
      snapshot.forEach((docSnap) => {
        const data = docSnap.data();
        if (data.downloadUrl) {
          allImages.push({
            url: data.downloadUrl,
            description: data.description?.length > 26 
              ? `${data.description.substring(0, 26)}...` 
              : data.description || 'No description available',
          });
        }
      });
      const recentFiveImages = allImages.slice(0, 5);
      setSavedImages(recentFiveImages);
    });

    return () => {
      unsubscribe();
    };
  }, [userId, isOpen]);

  const handleGenerateImage = async () => {
    if (!prompt.trim()) {
      setError('Please provide a valid prompt.');
      return;
    }

    if (!userId || userId === 'anonymous') {
      setError('You must be logged in to generate images.');
      return;
    }

    setIsGenerating(true);
    setError(null);
    setProgress({ stage: 'initializing', message: PROGRESS_MESSAGES.initializing });

    try {
      // Phase 1: Initialize job and refine prompt
      const initResp = await fetch('/api/initializeImageGeneration', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt, userId }),
      });

      if (!initResp.ok) {
        throw new Error('Failed to initialize image generation');
      }

      const { jobId } = await initResp.json();

      // Phase 2: Process image generation
      setProgress({ stage: 'generating', message: PROGRESS_MESSAGES.generating });
      const processResp = await fetch('/api/processImageGeneration', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ jobId, userId }),
      });

      if (!processResp.ok) {
        const errorData = await processResp.json();
        throw new Error(errorData.error || 'Failed to generate image');
      }

      const response: ImageGenerationResponse = await processResp.json();
      const { imageUrl, success, namespace } = response;
      
      if (!success || !imageUrl || !namespace) {
        throw new Error('Image generation failed');
      }

      setGeneratedImageUrl(imageUrl);
      onImageGenerated(imageUrl);

      // Phase 3: Process with ByteStore indexing
      setProgress({ stage: 'indexing', message: PROGRESS_MESSAGES.indexing });
      try {
        const processFileResp = await fetch('/api/processFileImage', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            docId: namespace,
            userId: userId,
            fileName: `generated-image-${namespace}.png`,
            fileType: 'image/png',
            fileUrl: imageUrl,
            isImage: true,
            category: 'My Images'
          })
        });

        if (!processFileResp.ok) {
          const errorData = await processFileResp.json();
          console.error('ByteStore processing failed:', errorData);
          // Don't throw here - we still generated the image successfully
        } else {
          //console.log('ByteStore processing successful');
        }
      } catch (byteStoreError) {
        console.error('ByteStore processing error:', byteStoreError);
        // Don't throw - we still want to show the generated image
      }

      setProgress({ stage: 'complete', message: PROGRESS_MESSAGES.complete });

    } catch (err) {
      console.error('Error:', err);
      setProgress({ 
        stage: 'error', 
        message: err instanceof Error ? err.message : 'An error occurred during image generation'
      });
      setError(err instanceof Error ? err.message : 'An error occurred during image generation');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleViewAllImages = () => {
    router.push('/imageGallery');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="bg-[#2d1d3a] rounded-lg p-4 w-full max-w-4xl mx-auto relative my-auto flex flex-col justify-center">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-gray-400 hover:text-white"
        >
          <X className="w-6 h-6" />
        </button>

        <div className="flex gap-4 mb-6 items-center">
          {savedImages.length === 0 ? (
            <>
              {Array.from({ length: 5 }).map((_, index) => (
                <div
                  key={`placeholder-${index}`}
                  className="flex items-center justify-center w-24 h-24 border-2 border-gray-600 bg-[#1a1a1a] rounded-lg"
                >
                  <span className="text-gray-500 text-sm">Saved Image</span>
                </div>
              ))}
            </>
          ) : (
            <>
              {savedImages.map((img, idx) => (
                <div key={idx} className="relative">
                  <img
                    src={img.url}
                    alt={img.description}
                    className="w-24 h-24 object-cover rounded-lg border-2 border-gray-600"
                  />
                  <span className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 truncate">
                    {img.description}
                  </span>
                </div>
              ))}
              {savedImages.length < 5 && (
                <>
                  {Array.from({ length: 5 - savedImages.length }).map(
                    (_, index) => (
                      <div
                        key={`placeholder-${index}`}
                        className="flex items-center justify-center w-24 h-24 border-2 border-gray-600 bg-[#1a1a1a] rounded-lg"
                      >
                        <span className="text-gray-500 text-sm">Saved Image</span>
                      </div>
                    )
                  )}
                </>
              )}
            </>
          )}
          <button
            onClick={handleViewAllImages}
            className="ml-auto px-3 py-2 bg-indigo-500 text-white text-sm rounded hover:bg-indigo-600 transition-colors"
          >
            View all images
          </button>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 aspect-square max-h-[400px] border-2 border-dashed border-gray-600 rounded-lg bg-[#1a1a1a] flex items-center justify-center relative">
            {isGenerating ? (
              <div className="flex flex-col items-center justify-center h-full">
                <ProgressIndicator progress={progress} />
              </div>
            ) : generatedImageUrl ? (
              <img
                src={generatedImageUrl}
                alt="Generated"
                className="w-full h-full object-cover rounded-lg"
              />
            ) : (
              <span className="text-gray-500">Generated Image</span>
            )}
          </div>

          <div className="w-full md:w-72">
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Describe your image..."
              className="w-full h-full min-h-[200px] md:min-h-[400px] bg-[#1a1a1a] border-2 border-gray-600 rounded-lg p-4 text-gray-200 resize-none focus:outline-none focus:border-blue-500"
            />
          </div>
        </div>

        {error && <div className="mt-4 text-red-500 text-sm">{error}</div>}

        <button
          onClick={handleGenerateImage}
          disabled={isGenerating || !prompt.trim() || userId === 'anonymous'}
          className="mt-6 bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          <ImageIcon className="w-5 h-5" />
          {isGenerating ? 'Generating...' : 'Generate Image'}
        </button>
      </div>
    </div>
  );
}