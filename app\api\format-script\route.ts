// app/api/format-script/route.ts
import { NextResponse } from 'next/server';
// Adjust these import paths to your actual file locations
import { ScriptFormatterTool, ScriptLine, FormattedScript } from 'lib/tools/scriptFormatter';
import { markdownFormatterTool } from 'lib/tools/markdownFormatter';

// Ensure ScriptFormatterTool and markdownFormatterTool are designed to be
// instantiated and used server-side. If ScriptFormatterTool uses processWithGoogleAI,
// it will now correctly pick up the server-side GOOGLE_AI_API_KEY.

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { scriptContent } = body;

    if (!scriptContent || typeof scriptContent !== 'string') {
      return NextResponse.json({ error: 'scriptContent (string) is required in the request body' }, { status: 400 });
    }

    // Instantiate tools on the server
    const scriptFormatter = new ScriptFormatterTool();
    // const localMarkdownFormatter = markdownFormatterTool; // If it's a singleton object

    //console.log("API Route: Starting script formatting with AI...");

    const formattedScript: FormattedScript = await scriptFormatter.formatScript(scriptContent, {
      model: "gemini-2.5-pro-preview-05-06", // Or use getDefaultGoogleAIModel() if imported
      provider: "google"
    });

    //console.log("API Route: Script formatting successful, converting to markdown...");

    // Attempt to convert the formatted script to markdown using the generic tool
    let markdown = await markdownFormatterTool.formatScriptToMarkdown(formattedScript);

    const looksLikeJsonOrRaw = (md: string): boolean => {
      const trimmedMd = md.trim();
      return (trimmedMd.startsWith("{") && trimmedMd.endsWith("}")) ||
             (trimmedMd.startsWith("[") && trimmedMd.endsWith("]")) ||
             (md.includes('"title":') && md.includes('"lines":')); // Basic check for JSON structure
    };

    // If the markdownFormatterTool output seems like raw data or not well-structured,
    // or if we simply prefer our custom layout:
    if (looksLikeJsonOrRaw(markdown) || (formattedScript.lines && formattedScript.lines.length > 0) ) {
      //console.log("API Route: Detected unformatted markdown or preferring custom format, creating custom markdown structure...");

      let customMarkdown = `# ${formattedScript.metadata?.title || "Script"}\n\n`;

      if (formattedScript.metadata?.author) {
        customMarkdown += `By ${formattedScript.metadata.author}\n\n`;
      }

      if (formattedScript.metadata?.summary) {
        customMarkdown += `## Summary\n\n${formattedScript.metadata.summary}\n\n`;
      }

      if (formattedScript.metadata?.characters && formattedScript.metadata.characters.length > 0) {
        customMarkdown += "## Characters\n\n";
        formattedScript.metadata.characters.forEach((character: string) => { // Ensure character is typed as string
          customMarkdown += `- ${character}\n`;
        });
        customMarkdown += "\n";
      }

      customMarkdown += "## Script\n\n";

      formattedScript.lines.forEach((line: ScriptLine) => {
        if (line.character && line.character.trim() !== "" && line.text && line.text.trim() !== "") {
          customMarkdown += `<p><strong>${line.character.toUpperCase()}</strong></p>\n\n`;
          customMarkdown += `${line.text.trim()}\n\n`;

          if (line.notes && line.notes.trim() !== "") {
            customMarkdown += `*${line.notes.trim()}*\n\n`;
          }
        }
      });
      markdown = customMarkdown;
    }

    //console.log("API Route: Markdown generation complete.");
    return NextResponse.json({ formattedMarkdown: markdown });

  } catch (error) {
    console.error("API Route - Error formatting script:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred during script formatting.";
    // Return a more generic error to the client for security, but log the detailed one.
    return NextResponse.json({ error: "Failed to format script on the server.", details: errorMessage }, { status: 500 });
  }
}